# Netlify Deployment Guide for UpZera

## Problem Fixed
The contact and newsletter forms were failing on the hosted Netlify server because:
1. Next.js was configured with `output: 'export'` (static export)
2. Static exports don't support API routes (`/api/contact` and `/api/newsletter`)
3. The forms worked locally because `next dev` includes a development server

## Solution Implemented
Using **Netlify's Next.js Plugin** which provides full Next.js support including API routes on Netlify's platform.

## Files Created/Modified

### New Files:
- `netlify.toml` - Netlify configuration file with Next.js plugin

### Modified Files:
- `app/contact/page.tsx` - Better error handling
- `components/NewsletterSubscribe.tsx` - Better error handling
- `next.config.js` - Removed static export, added Netlify compatibility

## Deployment Steps

### 1. Environment Variables
In your Netlify dashboard, go to **Site settings > Environment variables** and add:

```
MAILGUN_API_KEY=**************************************************
MAILGUN_DOMAIN=mg.upzera.com
MAILGUN_FROM=UpZera <<EMAIL>>
MAILGUN_REGION=eu
```

### 2. Build Settings
In Netlify dashboard, set:
- **Build command**: `npm run build`
- **Publish directory**: Leave empty (handled by Next.js plugin)

### 3. Deploy
Push your changes to your repository. Netlify will automatically:
1. Install dependencies
2. Build the Next.js application
3. Deploy with full Next.js runtime support
4. API routes work natively at `/api/contact` and `/api/newsletter`

## How It Works

### Netlify Next.js Plugin
The `@netlify/plugin-nextjs` plugin:
- Provides full Next.js runtime support
- Handles API routes natively (no redirects needed)
- Supports server-side rendering
- Manages static assets automatically
- Handles environment variables securely

### Email Flow
1. **Contact Form**: Sends notification to admin team + confirmation to user
2. **Newsletter**: Sends notification to admin team + welcome email to subscriber

## Testing
After deployment:
1. Test contact form submission
2. Test newsletter subscription
3. Check email delivery to both admin and user emails

## Troubleshooting

### If forms still fail:
1. Check Netlify Function logs in dashboard
2. Verify environment variables are set correctly
3. Ensure Mailgun domain is verified
4. Check CORS headers in browser developer tools

### Common Issues:
- **500 errors**: Usually environment variable issues
- **CORS errors**: Functions should handle CORS automatically
- **Email not sending**: Check Mailgun API key and domain settings

## Benefits of This Solution
- ✅ Works with static export (faster loading)
- ✅ Serverless functions (cost-effective)
- ✅ Same email functionality as before
- ✅ Better error handling
- ✅ CORS handled automatically
- ✅ No changes needed to form UI/UX
