"use client";

import { useState } from 'react';
import { useTranslations } from 'next-intl';

interface SERPPreviewProps {
  title?: string;
  description?: string;
  url?: string;
}

export function SERPPreview({ title, description, url }: SERPPreviewProps) {
  const t = useTranslations('Metadata');
  
  const defaultTitle = title || t('title');
  const defaultDescription = description || t('description');
  const defaultUrl = url || 'https://upzera.com';

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-lg font-semibold mb-4 text-gray-800">Google Search Preview</h2>
      
      {/* Mock Google Search Result */}
      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
        <div className="space-y-1">
          {/* URL */}
          <div className="text-sm text-green-700 font-normal">
            {defaultUrl}
          </div>
          
          {/* Title */}
          <h3 className="text-xl text-blue-800 hover:underline cursor-pointer font-normal leading-6">
            {defaultTitle}
          </h3>
          
          {/* Description */}
          <p className="text-sm text-gray-600 leading-5 mt-1">
            {defaultDescription}
          </p>
          
          {/* Metadata info */}
          <div className="text-xs text-gray-500 mt-2 pt-2 border-t border-gray-200">
            Character count: Title ({defaultTitle.length}/60) | Description ({defaultDescription.length}/160)
          </div>
        </div>
      </div>
      
      {/* SEO Tips */}
      <div className="mt-4 text-xs text-gray-500">
        <p><strong>Tips:</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li>Title should be 50-60 characters</li>
          <li>Description should be 150-160 characters</li>
          <li>Include your main keyword in both</li>
        </ul>
      </div>
    </div>
  );
}