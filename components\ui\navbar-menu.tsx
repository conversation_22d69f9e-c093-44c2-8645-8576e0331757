"use client";
import React from "react";
import { motion } from "framer-motion";
import { Link } from "@/i18n/navigation";
import Image from "next/image";

const transition = {
  type: "spring",
  mass: 0.5,
  damping: 11.5,
  stiffness: 100,
  restDelta: 0.001,
  restSpeed: 0.001,
};

export const MenuItem = ({
  setActive,
  active,
  item,
  children,
  isActive = false,
}: {
  setActive: (item: string) => void;
  active: string | null;
  item: string;
  children?: React.ReactNode;
  isActive?: boolean;
}) => {
  return (
    <div onMouseEnter={() => setActive(item)} className="relative">
      <motion.p
        transition={{ duration: 0.3 }}
        className="cursor-pointer text-white/90 hover:text-white px-3 py-2 relative font-medium"
      >
        {item}
        {isActive && (
          <span className="absolute bottom-0 left-3 right-3 h-[2px] transition-all duration-300 bg-pink-500 z-10 shadow-[0_0_3px_rgba(236,72,153,0.7)]"></span>
        )}
      </motion.p>
      {active !== null && (
        <motion.div
          initial={{ opacity: 0, scale: 0.85, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={transition}
        >
          {active === item && (
            <div className="absolute top-[calc(100%_+_1.2rem)] left-1/2 transform -translate-x-1/2 pt-4">
              <motion.div
                transition={transition}
                layoutId="active" // layoutId ensures smooth animation
                className="bg-purple-900 border-2 border-purple-700 backdrop-blur-sm rounded-xl overflow-hidden shadow-xl"
              >
                <motion.div
                  layout // layout ensures smooth animation
                  className="w-max h-full"
                >
                  {children}
                </motion.div>
              </motion.div>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export const Menu = ({
  setActive,
  children,
}: {
  setActive: (item: string | null) => void;
  children: React.ReactNode;
}) => {
  return (
    <nav
      onMouseLeave={() => setActive(null)} // resets the state
      className="relative rounded-full border border-transparent bg-transparent flex justify-center space-x-2 px-8 py-4"
    >
      {children}
    </nav>
  );
};

export const ProductItem = ({
  title,
  description,
  href,
  src,
}: {
  title: string;
  description: string;
  href: string;
  src: string;
}) => {
  return (
    <Link href={href} className="flex space-x-2">
      <Image
        src={src}
        width={140}
        height={70}
        alt={title}
        className="flex-shrink-0 rounded-md shadow-2xl"
      />
      <div>
        <h4 className="text-xl font-bold mb-1 text-white">
          {title}
        </h4>
        <p className="text-white/70 text-sm max-w-[10rem]">
          {description}
        </p>
      </div>
    </Link>
  );
};

export const HoveredLink = ({ children, ...rest }: any) => {
  return (
    <Link
      {...rest}
      className="text-white/70 hover:text-white transition-colors"
    >
      {children}
    </Link>
  );
};

export const NavItem = ({
  item,
  href,
  isActive,
}: {
  item: string;
  href: string;
  isActive: boolean;
}) => {
  return (
    <div className="relative">
      <Link
        href={href}
        className={`px-3 py-2 transition-all duration-200 block relative text-sm font-medium ${
          isActive ? "text-white font-semibold" : "text-white/80 hover:text-white"
        }`}
      >
        {item}
        {isActive && (
          <span className="absolute bottom-0 left-3 right-3 h-[2px] transition-all duration-300 bg-pink-500 z-10 shadow-[0_0_3px_rgba(236,72,153,0.7)]"></span>
        )}
      </Link>
    </div>
  );
};
