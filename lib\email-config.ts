// Centralized email configuration for UpZera
// This file contains all email addresses and configurations used throughout the application

/**
 * Admin email addresses that receive notifications from various forms and submissions
 * These can be overridden by environment variables for different deployment environments
 */
export function getAdminEmails(): string[] {
  // Check if admin emails are configured via environment variable
  const envEmails = process.env.ADMIN_EMAILS;
  
  if (envEmails) {
    // Split by comma and trim whitespace
    return envEmails.split(',').map(email => email.trim()).filter(email => email.length > 0);
  }
  
  // Default admin emails
  return [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
}

/**
 * Get the primary contact email (used for display purposes)
 */
export function getPrimaryContactEmail(): string {
  return process.env.PRIMARY_CONTACT_EMAIL || '<EMAIL>';
}

/**
 * Get the support email (used for fallback contact information)
 */
export function getSupportEmail(): string {
  return process.env.SUPPORT_EMAIL || '<EMAIL>';
}

/**
 * Email configuration object with all email-related settings
 */
export const emailConfig = {
  // Admin emails for notifications
  getAdminEmails,
  
  // Contact emails
  primaryContact: getPrimaryContactEmail(),
  support: getSupportEmail(),
  
  // Sender configuration (already handled in email-service.ts via MAILGUN_FROM)
  defaultSender: process.env.MAILGUN_FROM || 'UpZera <<EMAIL>>',
} as const;

export default emailConfig;