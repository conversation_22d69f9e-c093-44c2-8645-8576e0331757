"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils"; // Assuming cn utility is in lib/utils

interface HeroBackgroundProps extends React.HTMLAttributes<HTMLDivElement> {
  gradient?: boolean;
  blur?: boolean;
  children: React.ReactNode;
}

const HeroBackground = React.memo(React.forwardRef<HTMLDivElement, HeroBackgroundProps>(
  (
    {
      className,
      gradient = true, // Default to true as in original
      blur = true, // Default to true as in original
      children,
      ...props
    },
    ref
  ) => {
    return (
      // Removed overflow-hidden to allow glow spill
      <div ref={ref} className={cn("relative z-0", className)} {...props}>
        {/* The background effect container - Ensure it's behind content */}
        {gradient && (
          // Using z-[-1] to force this behind the relative children wrapper
          <div className="absolute inset-0 -z-10 flex w-full flex-1 items-start justify-center overflow-hidden pointer-events-none">
            {blur && (
              // Kept blur effect, ensure it's also behind
              <div className="absolute top-0 h-48 w-full bg-transparent opacity-10 backdrop-blur-md" />
            )}

            {/* Main glow - Changed to purple */}
            {/* Adjusted translate-y to lower the glow slightly */}
            <div
              className="absolute inset-auto z-0 h-36 w-[28rem] -translate-y-[10%] rounded-full bg-purple-500/60 opacity-80 blur-3xl"
              style={{ willChange: 'transform, opacity' }}
            />

            {/* Lamp effect - Changed to purple */}
            {/* Adjusted translate-y to lower the lamp effect */}
            <motion.div
              initial={{ width: "8rem" }}
              viewport={{ once: true }}
              transition={{ ease: "easeInOut", delay: 0.3, duration: 0.8 }}
              whileInView={{ width: "16rem" }}
              className="absolute top-0 z-0 h-36 translate-y-[0%] rounded-full bg-purple-500/60 blur-2xl" /* Changed from -translate-y-[20%] */
              style={{ willChange: 'transform, opacity' }}
            />

            {/* Top line - Changed to purple */}
            {/* Adjusted translate-y to lower the top line */}
            <motion.div
              initial={{ width: "15rem" }}
              viewport={{ once: true }}
              transition={{ ease: "easeInOut", delay: 0.3, duration: 0.8 }}
              whileInView={{ width: "30rem" }}
              // Lowered z-index and adjusted translate-y
              className="absolute inset-auto z-0 h-0.5 translate-y-[5%] bg-purple-500/60" /* Changed from -translate-y-[-10%] */
            />

            {/* Left gradient cone - Changed to purple */}
            <motion.div
              initial={{ opacity: 0.5, width: "15rem" }}
              whileInView={{ opacity: 1, width: "30rem" }}
              transition={{
                delay: 0.3,
                duration: 0.8,
                ease: "easeInOut",
              }}
              style={{
                backgroundImage: `conic-gradient(var(--conic-position), var(--tw-gradient-stops))`,
              }}
              // Lowered z-index
              className="absolute inset-auto right-1/2 z-0 h-56 overflow-visible w-[30rem] bg-gradient-conic from-purple-500/60 via-transparent to-transparent text-white [--conic-position:from_70deg_at_center_top]"
            >
              {/* Changed mask bg to white to match parent section */}
              <div className="absolute w-[100%] left-0 bg-white h-40 bottom-0 z-10 [mask-image:linear-gradient(to_top,white,transparent)]" />
              <div className="absolute w-40 h-[100%] left-0 bg-white bottom-0 z-10 [mask-image:linear-gradient(to_right,white,transparent)]" />
            </motion.div>

            {/* Right gradient cone - Changed to purple */}
            <motion.div
              initial={{ opacity: 0.5, width: "15rem" }}
              whileInView={{ opacity: 1, width: "30rem" }}
              transition={{
                delay: 0.3,
                duration: 0.8,
                ease: "easeInOut",
              }}
              style={{
                backgroundImage: `conic-gradient(var(--conic-position), var(--tw-gradient-stops))`,
              }}
              // Lowered z-index
              className="absolute inset-auto left-1/2 z-0 h-56 w-[30rem] bg-gradient-conic from-transparent via-transparent to-purple-500/60 text-white [--conic-position:from_290deg_at_center_top]"
            >
              {/* Changed mask bg to white to match parent section */}
              <div className="absolute w-40 h-[100%] right-0 bg-white bottom-0 z-10 [mask-image:linear-gradient(to_left,white,transparent)]" />
              <div className="absolute w-[100%] right-0 bg-white bottom-0 z-10 [mask-image:linear-gradient(to_top,white,transparent)]" />
            </motion.div>
          </div>
        )}
        {/* Render children above the background effect using default stacking */}
        <div className="relative">{children}</div>
      </div>
    );
  }
));
HeroBackground.displayName = "HeroBackground";

export { HeroBackground };
