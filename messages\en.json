{"Common": {"contactUs": "Contact Us", "getStarted": "Get Started", "learnMore": "Learn More", "bookMeeting": "Book Meeting", "viewPricing": "View Pricing", "ourServices": "Our Services", "talkToHuman": "Talk to Human"}, "Metadata": {"title": "UpZera - Web Development & Intelligent Chatbot Solutions", "siteName": "UpZera", "description": "Custom websites and smart chatbots that grow your business. Fast, professional, and designed for results. From landing pages to lead generation - we build digital solutions that work.", "keywords": "web development, chatbot solutions, digital solutions, business automation, website design, lead generation, smart automation, Netherlands, startup solutions, custom websites"}, "Navigation": {"home": "Home", "about": "About Us", "services": "Services", "testimonials": "Testimonials", "faq": "FAQ", "contact": "Contact Us", "approach": "Approach", "websiteDevelopment": "Website Development", "chatbotIntegration": "Chatbot Integration", "menu": "<PERSON><PERSON>", "menuSubtitle": "Start by discussing your goals, challenges, and preferences.", "conceptIdeation": "Concept & Ideation", "conceptDescription": "We brainstorm and develop a detailed plan for your site.", "projectKickoff": "Project Kickoff", "freeConsultation": "Free Consultation", "websiteDevelopmentDesc": "Custom websites built with modern technologies", "chatbotIntegrationDesc": "AI-powered chatbots for customer engagement"}, "Footer": {"references": "References", "services": "Services", "socialNetworks": "Social networks", "contact": "Contact", "approach": "Approach", "aboutUs": "About us", "testimonials": "Testimonials", "faq": "FAQ", "contactUs": "Contact us", "websiteDevelopment": "Website development", "chatbotIntegration": "Chatbot Integration", "linkedin": "LinkedIn", "newsletter": "Subscribe to our newsletter", "copyright": "© 2025 UpZera. All rights reserved."}, "Chatbot": {"viewPricing": "View Pricing", "viewPricingDesc": "View our pricing and packages", "bookMeeting": "Book Meeting", "bookMeetingDesc": "Schedule a free consultation", "ourServices": "Our Services", "ourServicesDesc": "Learn about our solutions", "talkToHuman": "Talk to Human", "talkToHumanDesc": "Get personalized help", "welcomeMessage": "Hi there! 👋 I'm the UpZera assistant.", "welcomeDescription": "We build smart digital tools that move businesses forward. What would you like to know about?", "onlineStatus": "Online Now", "offlineStatus": "Offline", "replyPlaceholder": "Reply to UpBot", "quickActionsTitle": "What would you like to know about?", "quickActionServices": "🚀 Our Services", "quickActionPricing": "💰 Pricing", "quickActionBookMeeting": "📅 Book Meeting", "quickActionPortfolio": "💼 Portfolio", "quickActionServicesMessage": "Tell me about your services", "quickActionPricingMessage": "What are your prices?", "quickActionConsultationMessage": "I want to book a consultation", "quickActionPortfolioMessage": "Show me your previous work", "serviceOptionsTitle": "Choose a service to learn more:", "serviceWebDevelopment": "Full-Stack Web Development", "serviceChatbots": "Chatbot Lead Generation", "serviceWebDevelopmentMessage": "Tell me about your web development services", "serviceChatbotsMessage": "I want to know about your chatbot services", "leadFormTitle": "Contact Information", "leadFormName": "Name", "leadFormEmail": "Email", "leadFormSubmit": "Submit", "leadFormSubmitting": "Submitting...", "leadFormErrorEmail": "Please enter a valid email", "leadFormErrorName": "Please enter your name", "leadFormErrorSubmit": "Failed to submit. Please try again later.", "leadFormSuccess": "Thank you {name}! Our team will contact you shortly.", "bookingMessage": "Perfect! I'd love to help you schedule a consultation. Let me show you our booking calendar:", "servicesMessage": "Great! Here are our main services. Click on any service to learn more:", "supportTicketStart": "I'd be happy to connect you with our team! 🤝\n\nI'll help you create a support ticket so our team can provide personalized assistance.\n\nFirst, could you please tell me your **name**?", "supportTicketNamePrompt": "Great! Now, could you please provide your **email address**?", "supportTicketEmailPrompt": "Perfect! Now, please describe the issue or question you'd like our team to help you with:", "supportTicketConfirm": "Thanks! Let me confirm your support ticket details:\n\n**Name:** {name}\n**Email:** {email}\n**Issue:** {description}\n\nShould I create this support ticket for you?", "supportTicketSuccess": "Perfect! 🎉 Your support ticket has been created successfully.\n\n**Ticket Details:**\n• **Name:** {name}\n• **Email:** {email}\n• **Issue:** {description}\n\n📧 **Next Steps:**\n• Our team will review your request\n• You'll receive a response within 24 hours\n• We'll contact you at the provided email address\n\nThank you for reaching out to UpZera!", "supportTicketError": "I apologize, but there was an issue creating your support ticket. Please try again or contact us <NAME_EMAIL>.", "supportTicketRestart": "No problem! Would you like to start over with the support ticket information?", "supportTicketRestartConfirm": "Great! Let's start over. Could you please tell me your **name**?", "perfectConfirmDetails": "Perfect! ✅ Let me confirm your details:", "nameLabel": "Name:", "emailLabel": "Email:", "isInformationCorrect": "Is this information correct?", "perfectProvideDescription": "Perfect! 📧 Now, please provide a **detailed description** of the problem or issue you're experiencing.", "tipMoreDetails": "💡 **Tip:** The more details you provide, the better we can help you!", "thankYouDetailedDescription": "Thank you for the detailed description! 📝", "confirmSupportTicketDetails": "Let me confirm your support ticket details:", "problemLabel": "Problem:", "shouldCreateSupportTicket": "Should I create the support ticket with this information?", "supportTicketCreatedSuccess": "Perfect! ✅ I've created support ticket **{ticketNumber}** for you.", "nextStepsTitle": "🔍 **Next Steps:**", "teamWillReview": "• Our team will review your request", "responseWithin24Hours": "• We'll respond within 24 hours", "checkEmailForUpdates": "• Check your email for updates", "thankYouForContacting": "Thank you for contacting UpZera!", "confirmDetailsPrompt": "Great! 🎉 I'm connecting you with our team.\n\n📞 **Next Steps:**\n• Someone will reach out to you shortly\n• We'll contact you at the provided email address\n\nThank you for choosing UpZera!", "reenterDetailsPrompt": "No problem! Would you like to re-enter your details?", "invalidEmailMessage": "That doesn't look like a valid email address. Could you please provide a valid email?", "pleaseConfirmMessage": "Please answer with 'yes' if the information is correct, or 'no' if you'd like to make changes.", "pleaseConfirmReenterMessage": "Please answer with 'yes' if you'd like to re-enter your details, or 'no' if you'd prefer to do something else.", "pleaseConfirmTicketMessage": "Please answer with 'yes' to create the support ticket, or 'no' if you'd like to make changes.", "pleaseConfirmRestartMessage": "Please answer with 'yes' if you'd like to start over, or 'no' if you'd prefer to do something else.", "endConversationConfirm": "I understand you'd like to end our conversation. Are you sure you don't need any other assistance today?", "endConversationGoodbye": "It was a pleasure to chat with you! Feel free to reach out anytime if you have more questions. Have a great day! 👋", "continueConversation": "Perfect! I'm here to help. What would you like to know more about?", "anythingElseYes": "Great! What else would you like to know?", "anythingElseNo": "I understand! Before we end our chat, are you sure you don't need any other assistance today? 🤔", "helpMenuResponses": ["I'd be happy to help you with that! Here are the main ways I can assist you:", "Let me help you find what you're looking for! Here are the main services I can assist with:", "I'm here to help! Here are the main areas where I can provide assistance:", "Great question! Let me show you the main ways I can help:", "I'd love to assist you! Here are the key areas I can help with:", "No problem! Here are the main services I can help you with:", "I'm ready to help! Here are the primary ways I can assist you:", "Let me guide you to the right place! Here are the main options:"], "followUpPricing": "Would you like to know more about any specific service pricing?", "followUpServices": "Would you like to know more about our other services?", "bookingYesResponse": "Great! To book a meeting, please let me know your preferred date and time, or you can check our booking calendar for availability.", "aiErrorMessage": "I'm having trouble connecting right now. Try asking about our services or book a free consultation!", "aiErrorFallback": "Let me connect you with our team! You can book a free consultation or email <NAME_EMAIL>.", "fallbackHelpMessage": "I'd be happy to help you with that! Let me show you what I can assist you with:", "restartConversation": "Start New Conversation", "conversationEnded": "Conversation Ended", "mainMenuPricesMessage": "I want to know about your prices", "mainMenuBookingMessage": "I want to book a meeting", "mainMenuServicesMessage": "Tell me about your services", "mainMenuHumanMessage": "I need to speak with a human", "mainMenuGreeting": "How can I help you today?", "wouldYouLikeToKnowMoreServices": "Would you like to know more about our other services?", "wouldYouLikeToKnowMorePricing": "Would you like to know more about any specific service pricing?", "greatToBookMeeting": "Great! To book a meeting, please let me know your preferred date and time, or you can check our booking calendar for availability.", "makeSureUnderstandCorrectly": "I want to make sure I understand correctly. Do you need any other assistance today, or are you ready to end our chat?", "happyToHelpMainWays": "I'd be happy to help you with that! Here are the main ways I can assist you:", "letMeHelpFindServices": "Let me help you find what you're looking for! Here are the main services I can assist with:", "hereToHelpMainAreas": "I'm here to help! Here are the main areas where I can provide assistance:", "greatQuestionMainWays": "Great question! Let me show you the main ways I can help:", "loveToAssistKeyAreas": "I'd love to assist you! Here are the key areas I can help with:", "noProblemMainServices": "No problem! Here are the main services I can help you with:", "readyToHelpPrimaryWays": "I'm ready to help! Here are the primary ways I can assist you:", "letMeGuideMainOptions": "Let me guide you to the right place! Here are the main options:", "understandEndConversation": "Are you sure you don't need help with anything else?", "happyToHelpShowAssist": "I'd be happy to help you with that! Let me show you what I can assist you with:", "greatWhatElseKnow": "Great! What else would you like to know?", "understandBeforeEndChat": "I understand! Before we end our chat, are you sure you don't need any other assistance today? 🤔", "invalidEmailAddress": "That doesn't look like a valid email address. Could you please provide a valid email?", "greatConnectingTeam": "Great! 🎉 I'm connecting you with our team.\n\n📞 **Next Steps:**\n• Someone will reach out to you shortly\n• We'll contact you at the provided email address\n\nThank you for choosing UpZera!", "noProblemReenterDetails": "No problem! Would you like to re-enter your details?", "pleaseAnswerYesNoCorrect": "Please answer with 'yes' if the information is correct, or 'no' if you'd like to make changes.", "pleaseAnswerYesNoReenter": "Please answer with 'yes' if you'd like to re-enter your details, or 'no' if you'd prefer to do something else.", "apologizeIssueCreatingTicket": "I apologize, but there was an issue creating your support ticket. Please try again or contact us <NAME_EMAIL>.", "noProblemStartOverTicket": "No problem! Would you like to start over with the support ticket information?", "pleaseAnswerYesNoCreateTicket": "Please answer with 'yes' to create the support ticket, or 'no' if you'd like to make changes.", "greatStartOverName": "Great! Let's start over. Could you please tell me your **name**?", "pleaseAnswerYesNoStartOver": "Please answer with 'yes' if you'd like to start over, or 'no' if you'd prefer to do something else.", "pleasureToChat": "It was a pleasure to chat with you! Feel free to reach out anytime if you have more questions. Have a great day! 👋", "perfectHereToHelp": "Perfect! I'm here to help. What would you like to know more about?", "letUsKnowAnythingElse": "Let us know if you need anything else! 😊", "areYouStillThere": "Are you still there? I'm here if you need any help! 😊", "beenAwayForAWhile": "It looks like you've been away for a while. Feel free to start a new conversation anytime you need help! 👋", "noProblemAnythingElse": "No problem! Is there anything else I can help you with?", "gotIt": "Got it!", "happyToHelpLearnServices": "I'd be happy to help you learn more about our services.", "anythingElseToday": "Is there anything else I can help you with today?", "sureStartOverName": "Sure! Let's start over. What's your name?", "noWorriesAnythingElse": "No worries! Is there anything else I can help you with?", "noProblemAnythingElseToday": "No problem! Is there anything else I can help you with today?", "chatHasEnded": "Cha<PERSON> has ended", "startNewChat": "Start new chat", "niceToMeetYouEmailPrompt": "Nice to meet you, {name}! Now, could you please provide your email address?", "thankYouEmailPrompt": "Thank you, {name}! Now, could you please provide your email address?", "fallbackGreeting": "Hello! How can I help you today?"}, "FAQ": {"title": "Frequently Asked Questions", "description": "Find answers to common questions about our services and approach", "q1": "What kind of businesses do you work with?", "a1": "We typically work with startups, small teams, and growing businesses that need fast, reliable digital tools — from landing pages to more complex designs. Whether you're just starting or scaling, we're here to help.", "q2": "How much do your services cost?", "a2": "You'll receive a clear, personalized quote after a free consultation.<br />Our pricing is project-based and depends on your specific goals and scope. Most projects range from €350–€500 for landing pages, €1,400-€2,400 for full websites, and €500-€1,200 for chatbot setups.", "q3": "How fast can you deliver a project?", "a3": "Timelines vary based on complexity, but most landing pages are ready within a few days, while full builds typically take 2–4 weeks. We'll give you a clear timeline before we start — and we stick to it.", "q4": "Do you offer ongoing support?", "a4": "Absolutely — but only when you need it. We're here post-launch for fixes, updates, or improvements. You won't be handed off to a helpdesk — just reach out and we'll help.", "q5": "What makes you different from other web development teams?", "a5": "We're engineers at heart, obsessed with clean solutions. We use agentic AI workflows to deliver faster without sacrificing quality — and we tailor everything to your business, not some cookie-cutter template.", "q6": "How do I get started?", "a6": "Simple - just book a free consultation. We'll talk about your goals, assess your needs, and give you a custom game plan.", "q7": "How do you handle invoicing and payment?", "a7": "<div class=\"space-y-4\"><p>We keep payments simple and transparent:</p><ul class=\"list-disc list-inside space-y-2 pl-4\"><li><b>Free concept preview</b> - We'll create a small design preview to ensure we're the right fit</li><li><b>Contract signing</b> - If you like the direction, we'll formalize the agreement</li><li><b>Deposit payment</b> - 20% for small projects or milestone-based for larger ones</li><li><b>Clear invoicing</b> - Based on agreed milestones or final delivery</li><li><b>Flexible terms</b> - Standard NET15 payment terms, but we can accommodate your needs</li></ul><p>No surprises — just straightforward payment terms that work for both of us.</p></div>", "q8": "What's needed to start a project?", "a8": "<div class=\"space-y-4\"><p>Our simplified 6-step process:</p><ol class=\"list-decimal list-inside space-y-3 pl-4\"><li class=\"font-medium\">Introduction<br /><span class=\"font-normal text-purple-100/80\">A brief call to understand your business, goals, and challenges</span></li><li class=\"font-medium\">Concept and demo<br /><span class=\"font-normal text-purple-100/80\">We share ideas and, if needed, a free demo to align on direction</span></li><li class=\"font-medium\">Scope and agreement<br /><span class=\"font-normal text-purple-100/80\">We define the scope, timeline, and pricing, then sign the agreement</span></li><li class=\"font-medium\">Kickoff<br /><span class=\"font-normal text-purple-100/80\">We plan the phases and begin development</span></li><li class=\"font-medium\">Development<br /><span class=\"font-normal text-purple-100/80\">We work in stages, share progress, and collect feedback</span></li><li class=\"font-medium\">Delivery and support<br /><span class=\"font-normal text-purple-100/80\">We deliver the solution and remain available for future support</span></li></ol><p>Simple, transparent, and focused on your goals.</p></div>", "stillHaveQuestions": "Still have questions?", "stillHaveQuestionsDesc": "We're here to help with any questions you might have about our services"}, "HomePage": {"heroTitle": "We Build Digital Solutions That Grow Your Business with", "heroHighlight": "zero effort", "heroSubtitle": "UpZera helps you scale with AI-powered solutions that bring together websites, chatbots into a single seamless experience.", "launchWithUs": "Launch with us", "freeConsultation": "Free consultation", "fastTurnaround": "Fast turnaround", "coreServicesTitle": "Our Core Services", "fullStackTitle": "Full-Stack Web Development", "fullStackDesc": "From design to backend logic — we build fully functional websites that look great and work even better.", "chatbotsTitle": "Chatbots & Lead Generation", "chatbotsDesc": "Capture leads, qualify them, and respond instantly with custom-built chatbots that work 24/7.", "supportTitle": "Ongoing Support", "supportDesc": "We don't disappear after launch — we're here to maintain, improve, and grow your solution with you.", "deploymentTitle": "Lightning Fast Deployment", "deploymentDesc": "Get your solutions up and running quickly with our streamlined deployment process.", "techStackTitle": "Our", "techStackHighlight": "Tech Stack", "techStackDesc": "We leverage modern technologies to build robust, scalable, and high-performance web solutions.", "whyChooseTitle": "Why Choose UpZera?", "otherSpecialists": "Other Specialists", "limitedResultsTitle": "Limited results and high costs:", "limitedResultsDesc": "Charge premium prices for basic solutions, focus on appearance over performance/scalability, don't tailor service.", "confusingProcessTitle": "Confusing process, unclear ownership:", "confusingProcessDesc": "Overcomplicate with jargon, unclear who builds the product, slow handoffs.", "oneWayCommTitle": "One-way communication:", "oneWayCommDesc": "Hard to reach, slow replies, inflexible working methods.", "weAtUpZera": "We at UpZera are", "affordableTitle": "Affordable, tailored, and full-stack:", "affordableDesc": "Fair pricing, full-cycle solutions, customized to your needs.", "clearProcessTitle": "Clear process, direct access:", "clearProcessDesc": "Built by us, simple explanations, clear timelines, always know the status.", "personalTitle": "Personal and responsive:", "personalDesc": "Direct access to makers, quick communication via preferred channels, collaborative and transparent.", "ctaTitle": "Got an idea? Let's explore it together.", "ctaDesc": "Contact us to schedule a free project consultation.", "useFreeConsultation": "Use a free consultation", "floatingKeyword1": "CONVERSION-FOCUSED", "floatingKeyword2": "ZERO EFFORT", "floatingKeyword3": "AGENTIC-AI", "floatingKeyword4": "START TODAY!"}, "AboutPage": {"heroTitle": "So… who are we, really?", "heroSubtitle": "A team that's more about people than pixels. We build with care, clarity, and a whole lot of curiosity.", "ourStoryTitle": "Our Story", "story1": "UpZera kicked off in 2025 with one mission: build smart digital tools that actually move businesses forward — fast, clean, and with purpose.", "story2": "We're a lean, tech-obsessed team building websites and chatbots. By integrating agentic AI workflows into our process, we're able to deliver faster without ever cutting corners — speed and reliability, side by side.", "story3": "We take you from idea to launch — and when you need us, we're ready to step in and keep things running smooth. No vanishing acts. No handoffs to some faceless support line. Just real people, ready to help when it counts.", "story4": "Every solution we ship is tailor-made — no bloated templates, no fluff. Just smart, clean builds designed around your business, your story, and your next big move.", "tagline": "✨ Elevate your business with Zero effort.", "teamTitle": "Led by <PERSON><PERSON>ous Engineers", "teamSubtitle": "UpZera is represented by two passionate engineering students — one specializing in Electrical Engineering, the other in Computer Science. We combine technical depth with a strong sense of design to create reliable, efficient, and modern solutions.", "edgarasRole": "CEO", "edgarasDesc": "Electrical Engineering student with a passion for clean code and efficient systems.", "viliusRole": "CTO", "viliusDesc": "Computer Science student with enthusiasm for innovative ideas.", "valuesTitle": "What We Believe In", "valuesInteractiveTitle": "These values are the foundation of everything we do.", "honestyTitle": "<PERSON><PERSON><PERSON>", "honestyDesc": "We say what we mean, and we build what we promise.", "responsibilityTitle": "Responsibility", "responsibilityDesc": "Your project is our project. We treat it with the same care.", "clientCareTitle": "Client Care", "clientCareDesc": "We listen first, build second. Your needs come first, always.", "trustworthinessTitle": "Trustworthiness", "trustworthinessDesc": "We believe trust is earned. We're here for the long run.", "servicesTitle": "Full-Cycle Development, Built for Growth", "webDevTitle": "Web Development", "webDesign": "Web Design", "uiUxImplementation": "UI/UX Implementation", "backendTitle": "Backend & Automations", "backendLogic": "Backend Logic", "processAutomation": "Process Automation", "chatbotsTitle": "Chatbots & Lead Tools", "aiIntegrations": "AI Integrations", "leadGeneration": "Lead Generation", "integrationsTitle": "Smart Integrations", "apis": "APIs", "calendarBooking": "Calendar/Booking tools", "performanceTitle": "Performance & SEO", "optimization": "Optimization", "searchVisibility": "Search Visibility", "supportTitle": "Ongoing Support", "maintenance": "Maintenance", "updatesImprovements": "Updates & Improvements", "ctaFinalTitle": "Enough About Us — Let's Talk About You", "ctaFinalDesc": "Ready to elevate your digital presence? Let's start a conversation about your business goals.", "getFreeQuote": "Get Your Free Quote"}, "ContactPage": {"heroTitle": "Do not hesitate to get in touch", "heroSubtitle": "We will do our best to answer all your questions and provide you with our services", "email": "Email", "phone": "Phone", "address": "Address", "contactUs": "Contact us", "name": "Name", "namePlaceholder": "Your name", "emailPlaceholder": "<EMAIL>", "service": "Service", "message": "Message", "messagePlaceholder": "Your message", "sending": "Sending...", "contact": "Contact", "chatbotIntegration": "Chatbot Integration", "chatbotIntegrationDesc": "AI-powered chat solutions", "webDevelopment": "Web Development", "webDevelopmentDesc": "Custom website solutions", "otherServices": "Other Services", "otherServicesDesc": "Custom requirements", "mvpVirtualAssistant": "MVP Virtual Assistant", "mvpVirtualAssistantDesc": "Essential chatbot features for your business", "customizableAiAssistant": "Customizable AI Assistant", "customizableAiAssistantDesc": "Advanced features with deep integrations", "websiteEssentials": "Website Essentials", "websiteEssentialsDesc": "Basic website with core functionality", "smartBusinessWebsites": "Smart Business Websites", "smartBusinessWebsitesDesc": "Enhanced features for growing businesses", "advancedWebPlatforms": "Advanced Web Platforms", "advancedWebPlatformsDesc": "Complex solutions with custom functionality", "selectedServices": "Selected Services", "nameRequired": "Name is required.", "emailRequired": "Email is required.", "emailInvalid": "<PERSON><PERSON> is invalid.", "serviceRequired": "Please select at least one service.", "messageRequired": "Message is required.", "thankYou": "Thank you! Your message has been sent.", "orTitle": "OR!", "scheduleMeeting": "Schedule a Meeting", "scheduleMeetingDesc": "Book a free consultation call with our team"}, "ApproachPage": {"title": "Our Approach", "subtitle": "We plan, build, and support your project every step of the way.", "step1Title": "1. Discovery Call", "step1Description": "We start with a short conversation to understand your goals, challenges, and vision for the project.", "step2Title": "2. Concept & Optional Demo", "step2Description": "We brainstorm ideas and, if requested, provide a small, free of charge, demo to make sure we're aligned before moving forward.", "step3Title": "3. Scope, Pricing & Payment", "step3Description": "We define the project scope, timeline, and pricing. Once agreed, we sign the contract and collect a deposit.", "step4Title": "4. <PERSON>", "step4Description": "With everything in place, development begins. We schedule milestones and get to work.", "step5Title": "5. Build & Feedback", "step5Description": "We build in stages, sharing progress and collecting your input to keep things on track and on point.", "step6Title": "6. Delivery & Support", "step6Description": "We finalize and hand over your project, offer support, and stay available for future collaboration."}, "Newsletter": {"emailPlaceholder": "Your email address", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "successMessage": "Thank you for subscribing to our newsletter!"}, "TestimonialsPage": {"heroTitle": "Client Success Stories", "heroSubtitle": "See how we've helped businesses transform their digital presence and achieve their goals.", "featuredProject": "Featured Project: Qochi Services", "projectTitle": "How We Helped Qochi Launch a Clean, Modern Tutoring Platform", "testimonial1Quote": "UpZera made the whole process smooth. The website is fast, stylish, and my students love how easy it is to book sessions.", "testimonial1Author": "— Founder of Qochi Services", "checkItOut": "Check it out →", "challengeTitle": "The Challenge", "challengeDescription": "Qochi needed a clean, modern website to showcase their tutoring services — with the ability for students to easily book sessions online and engage with the brand.", "deliveredTitle": "What We Delivered", "delivered1Title": "Responsive, fast-loading website", "delivered1Description": "With a sleek, professional layout", "delivered2Title": "Booking integration using Calendly", "delivered2Description": "Allowing clients to reserve tutoring sessions effortlessly", "delivered3Title": "Thoughtful content layout", "delivered3Description": "Focused on trust, clarity, and conversion", "delivered4Title": "Simple, elegant design system", "delivered4Description": "That reflects the educational tone of <PERSON><PERSON><PERSON>'s brand", "techStackTitle": "Tech Stack", "resultsTitle": "Results", "result1": "Live in just a few days", "result2Title": "Fully mobile-friendly", "result2Description": "Perfect scores on mobile usability tests", "result3": "Smooth booking experience", "result4": "Owner can manage everything with ease", "clientQuoteTitle": "From the Client", "clientQuote": "The UpZera team made it feel effortless. They understood exactly what I needed, and delivered even better than I imagined.", "ctaTitle": "Want to be our next success story?", "ctaDescription": "Let's turn your idea into a live product — fast, custom, and fully done-for-you.", "ctaButton": "Book your free consultation now!"}, "EmailTemplates": {"contactForm": {"notificationSubject": "New Contact Form Submission!! - {service}", "notificationTitle": "New Contact Form Submission", "notificationDescription": "A new inquiry has been received from your website contact form.", "contactDetails": "Contact Details", "name": "Name", "email": "Email", "service": "Service", "message": "Message", "confirmationSubject": "Thank you for contacting UpZera!", "confirmationTitle": "Thank you for your inquiry!", "confirmationGreeting": "Hello {name}!", "confirmationMessage": "We've received your message and will get back to you within 24 hours. Our team will review your inquiry and provide you with a detailed response.", "confirmationNextSteps": "What's next?", "confirmationStep1": "Our team will review your inquiry", "confirmationStep2": "We'll get back to you within 24 hours", "confirmationStep3": "We'll discuss your project and requirements", "immediateAssistance": "Need immediate assistance?", "contactUsAt": "Contact us at", "bestRegards": "Best regards,", "teamSignature": "The UpZera Team"}, "newsletter": {"notificationSubject": "New Newsletter Subscription", "notificationTitle": "New Newsletter Subscription", "notificationDescription": "A new subscriber has signed up for your newsletter.", "subscriberEmail": "Subscriber Email", "confirmationSubject": "Welcome to UpZera Newsletter!", "confirmationTitle": "Successfully subscribed!", "confirmationMessage": "Thank you for joining the UpZera community! You'll receive the latest updates about our services, technology trends, and exclusive offers.", "whatToExpect": "What to expect:", "expectation1": "Weekly technology updates", "expectation2": "Exclusive offers and discounts", "expectation3": "Tips and best practices", "expectation4": "Early access to new features", "unsubscribeNote": "You can unsubscribe at any time by clicking the link at the bottom of our emails."}, "supportTicket": {"notificationSubject": "🎫 New Support Ticket #{ticketNumber} - {name}", "notificationTitle": "New Support Ticket", "notificationDescription": "A new support ticket has been received from a customer.", "ticketNumber": "Ticket Number", "customerInfo": "Customer Information", "problemDescription": "Problem Description", "conversationHistory": "Conversation History", "noConversationHistory": "No conversation history provided", "confirmationSubject": "Support Ticket Created - #{ticketNumber}", "confirmationTitle": "Your support ticket has been created!", "confirmationGreeting": "Hello {name}!", "confirmationMessage": "We've successfully created your support ticket. Our team will review your issue and get back to you as soon as possible.", "ticketDetails": "Ticket Details", "yourTicketNumber": "Your Ticket Number", "status": "Status", "statusOpen": "Open", "nextSteps": "Next Steps", "nextStep1": "Our support team will review your issue", "nextStep2": "We'll contact you within 24 hours", "nextStep3": "We'll work together to resolve the problem", "trackTicket": "Track Your Ticket", "trackTicketDescription": "Save this ticket number for future reference"}, "common": {"copyrightText": "© {year} UpZera. All rights reserved.", "logoAlt": "UpZera Logo"}}, "WebsiteDevelopmentPage": {"heroTitle1": "We Don't Just Build Websites —", "heroTitle2": "We create intelligent digital experiences", "heroSubtitle": "From sleek one-pagers to more advanced platforms, we deliver websites that are fast, responsive, and fully functional.", "getStarted": "Get Started", "packagesTitle": "Our Web Development", "packagesHighlight": "Packages", "packagesSubtitle": "These packages are approximate classifications. In your free consultation, we'll help determine the exact level of sophistication your project requires.", "starterTitle": "Starter", "starterPrice": "€1,500 - €3,000", "starterDesc": "Perfect for small businesses, personal brands, or simple service-based companies that need a professional online presence.", "starterFeature1": "Up to 5 pages", "starterFeature2": "Responsive design", "starterFeature3": "Basic SEO setup", "starterFeature4": "Contact forms", "starterFeature5": "Social media integration", "starterFeature6": "Basic analytics", "professionalTitle": "Professional", "professionalPrice": "€3,000 - €7,000", "professionalDesc": "Ideal for growing businesses that need more functionality, better user experience, and room to scale.", "professionalFeature1": "Up to 15 pages", "professionalFeature2": "Advanced animations", "professionalFeature3": "CMS integration", "professionalFeature4": "E-commerce ready", "professionalFeature5": "Advanced SEO", "professionalFeature6": "Performance optimization", "professionalFeature7": "Third-party integrations", "professionalFeature8": "Custom functionality", "enterpriseTitle": "Enterprise", "enterprisePrice": "€7,000+", "enterpriseDesc": "For established businesses requiring complex functionality, custom integrations, and enterprise-level performance.", "enterpriseFeature1": "Unlimited pages", "enterpriseFeature2": "Custom development", "enterpriseFeature3": "API integrations", "enterpriseFeature4": "Advanced security", "enterpriseFeature5": "Multi-language support", "enterpriseFeature6": "Custom admin panels", "enterpriseFeature7": "Performance monitoring", "enterpriseFeature8": "Dedicated support", "getQuote": "Get Quote", "whyChooseTitle": "Why Choose UpZera for Your Website?", "feature1": "Fully Responsive Design", "feature2": "Speed-Optimized Performance", "feature3": "Solid SEO Foundations", "feature4": "Essential Security Setup", "feature5": "Transparent & Collaborative Process", "feature6": "Scalable foundations for future growth", "ctaTitle": "Want to elevate your business?", "ctaSubtitle": "Let's discuss how a tailored web solution can drive growth.", "ctaButton": "Book a free strategy call!", "workflowTitle": "Our AI-Enhanced Workflow", "workflowSubtitle": "Human-led, tool-assisted — for faster, smarter, better results.", "lightningFastTitle": "Lightning Fast", "lightningFastDesc": "Product can be delivered in days, not weeks with our streamlined process, but of course depending on the sophistication of the task", "smartWorkflowsTitle": "Smart Workflows", "smartWorkflowsDesc": "AI-enhanced development reduces time while increasing quality", "cleanDesignTitle": "Clean Design", "cleanDesignDesc": "Modern, responsive layouts that convert visitors", "seoOptimizedTitle": "SEO Optimized", "seoOptimizedDesc": "Built to rank from day one with smart content suggestions", "mobileFirstTitle": "Mobile-First", "mobileFirstDesc": "Perfectly responsive across all devices", "seamlessIntegrationsTitle": "Seamless Integrations", "seamlessIntegrationsDesc": "Chatbots, CRMs and more connect effortlessly", "techStackTitle": "Our Tech Stack", "techStackDesc": "We leverage modern technologies to build robust, scalable, and high-performance web solutions."}, "ChatbotIntegrationPage": {"mainTitle": "AI-Powered <PERSON>", "mainHighlight": "Integration", "mainDescription": "Enhance customer experience, automate support, and capture leads 24/7 with intelligent virtual assistants tailored to your business.", "heroTitle": "Meet Your", "heroHighlight": "AI Assistant", "getConsultation": "Get Free Consultation", "smartResponses": "Smart responses", "leadCapture": "Lead capture", "seamlessHandoff": "Seamless handoff", "whyChooseTitle": "Why Choose an UpZera Virtual Assistant?", "whyChooseDesc": "Our AI assistants are meticulously designed to enhance your business operations while delivering an exceptional user experience", "benefit1Title": "24/7 Instant Support", "benefit1Desc": "Your assistant never sleeps — it handles inquiries, FAQs, and support requests any time of day, improving response time and customer satisfaction.", "benefit2Title": "Lead Qualification & Routing", "benefit2Desc": "It asks smart questions, filters high-quality leads, and routes them to the right team or platform (CRM, email, etc.) — no manual sorting needed.", "benefit3Title": "Live Agent Escalation", "benefit3Desc": "When the request is too complex, it can seamlessly hand off the conversation to a real human — with all the context preserved for smooth continuity.", "benefit4Title": "Personalized Customer Experience", "benefit4Desc": "It remembers past interactions, preferences, and context to provide tailored responses that feel natural and helpful.", "benefit5Title": "Data Collection & Insights", "benefit5Desc": "Every conversation generates valuable data about customer needs, pain points, and behavior patterns to inform your business strategy.", "benefit6Title": "Cost-Effective Scaling", "benefit6Desc": "Handle more inquiries without hiring additional support staff — your chatbot grows with your business demands.", "mvpTitle": "MVP Virtual Assistant", "mvpDesc": "Get started quickly with a core virtual assistant tailored to your essential business needs. Ideal for automating FAQs and basic lead capture.", "mvpFeature1": "Basic FAQ automation", "mvpFeature2": "Lead capture forms", "mvpFeature3": "Email notifications", "mvpFeature4": "Simple conversation flows", "mvpFeature5": "Basic analytics", "customTitle": "Highly Customizable Vistual Assistant", "customDesc": "A fully customizable solution with advanced integrations (CRM, Calendar), complex workflows, and deeper context-aware conversations for maximum impact.", "customFeature1": "Advanced conversation AI", "customFeature2": "CRM integrations", "customFeature3": "Calendar booking", "customFeature4": "Multi-language support", "customFeature5": "Custom workflows", "customFeature6": "Advanced analytics", "customFeature7": "Live agent handoff", "customFeature8": "API integrations", "chooseSolutionTitle": "Choose Your Solution", "chooseSolutionSubtitle": "Select the right level of AI assistance for your business needs", "getStarted": "Get Started", "ctaTitle": "Ready to Transform Customer Interactions?", "ctaSubtitle": "Let's build an AI assistant that works perfectly for your business.", "ctaButton": "Start Your Chatbot Journey"}, "CookieConsent": {"title": "We use cookies on our website", "description": "To improve your browsing experience, for statistical and direct marketing purposes, we use cookies on this website, which you can revoke at any time by changing your internet browser settings and deleting saved cookies. If you do not agree, we will not store cookies on your device, but some website functions may partially or completely not work.", "acceptAll": "Accept", "declineAll": "Decline", "managePreferences": "Manage Preferences", "preferencesTitle": "Cookie Preferences", "essentialCookies": "Essential Cookies", "alwaysActive": "Always Active", "essentialDescription": "These cookies are necessary for the website to function properly. They enable basic features like page navigation and language settings.", "analyticsCookies": "Analytics Cookies", "analyticsDescription": "These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously. This helps us improve our site.", "cancel": "Cancel", "savePreferences": "Save Preferences"}}