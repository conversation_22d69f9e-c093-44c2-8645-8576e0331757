# Deployment Checklist for UpZera

## Pre-Deployment Checklist

### ✅ **Code Preparation**
- [x] Removed `output: 'export'` from `next.config.js` ✅
- [x] API routes exist in `app/api/contact/route.ts` and `app/api/newsletter/route.ts` ✅
- [x] Environment variables defined in `.env` ✅
- [x] Forms updated with better error handling ✅
- [ ] Test locally: `npm run dev` and verify forms work
- [ ] Test build: `npm run build && npm start`

### ✅ **Environment Variables Required**
```
MAILGUN_API_KEY=**************************************************
MAILGUN_DOMAIN=mg.upzera.com
MAILGUN_FROM=UpZera <<EMAIL>>
MAILGUN_REGION=eu
```

## Hosting Options (Choose One)

### 🚀 **Option 1: Vercel (Easiest)**
**Time**: 5 minutes

1. [ ] Go to [vercel.com](https://vercel.com)
2. [ ] Connect GitHub repository
3. [ ] Add environment variables in dashboard
4. [ ] Deploy automatically
5. [ ] Test forms on live site

**Pros**: Zero configuration, automatic optimization
**Cons**: Vendor lock-in

---

### 🛠️ **Option 2: Hostinger VPS (Most Control)**
**Time**: 30-60 minutes

#### **Server Setup**:
1. [ ] Purchase Hostinger VPS with Node.js support
2. [ ] Connect via SSH
3. [ ] Install Node.js 18+
4. [ ] Install Nginx
5. [ ] Install PM2 process manager

#### **App Deployment**:
1. [ ] Upload project files (Git/FTP)
2. [ ] Run `npm install`
3. [ ] Create `.env.production` with environment variables
4. [ ] Run `npm run build`
5. [ ] Start with PM2: `pm2 start npm --name "upzera" -- start`
6. [ ] Configure Nginx reverse proxy
7. [ ] Set up SSL certificate (optional)

**Pros**: Full control, cost-effective
**Cons**: Manual setup required

---

### ⚡ **Option 3: Railway (Middle Ground)**
**Time**: 10 minutes

1. [ ] Go to [railway.app](https://railway.app)
2. [ ] Connect GitHub repository
3. [ ] Add environment variables
4. [ ] Deploy automatically

**Pros**: Simple setup, good performance
**Cons**: Limited free tier

## Post-Deployment Testing

### ✅ **Test Contact Form**
1. [ ] Fill out contact form on live site
2. [ ] Check if form submits successfully
3. [ ] Verify admin emails received (<EMAIL>, <EMAIL>)
4. [ ] Verify user confirmation email received
5. [ ] Test with invalid data (should show errors)

### ✅ **Test Newsletter Form**
1. [ ] Submit newsletter subscription
2. [ ] Check if form submits successfully
3. [ ] Verify admin notification received
4. [ ] Verify user welcome email received
5. [ ] Test with invalid email (should show error)

### ✅ **General Site Testing**
1. [ ] All pages load correctly
2. [ ] Images display properly
3. [ ] Navigation works
4. [ ] Mobile responsiveness
5. [ ] SSL certificate working (https://)

## Troubleshooting Guide

### **Forms Not Working**
```bash
# Check server logs
pm2 logs upzera  # For VPS hosting

# Check browser console for errors
# Check network tab for failed requests
```

### **Environment Variables**
```bash
# Verify variables are loaded
console.log(process.env.MAILGUN_API_KEY)  # Should not be undefined
```

### **Build Issues**
```bash
# Clear cache and rebuild
rm -rf .next
rm -rf node_modules
npm install
npm run build
```

## Quick Commands Reference

### **Local Development**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm start           # Start production server
npm run lint        # Check for code issues
```

### **VPS Management**
```bash
pm2 status          # Check app status
pm2 restart upzera  # Restart app
pm2 logs upzera     # View logs
systemctl status nginx  # Check Nginx
```

## Success Criteria

Your deployment is successful when:
- ✅ Website loads at your domain
- ✅ Contact form sends emails to admin and user
- ✅ Newsletter form sends emails to admin and user
- ✅ No console errors in browser
- ✅ All pages accessible
- ✅ HTTPS working (if configured)

## Need Help?

If you encounter issues:
1. Check the logs (browser console, server logs)
2. Verify environment variables are set correctly
3. Test API endpoints directly: `https://yourdomain.com/api/contact`
4. Compare with working localhost version

Your app is now ready for deployment on any Node.js hosting provider! 🚀
