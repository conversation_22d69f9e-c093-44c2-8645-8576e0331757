import React from 'react';
import { useTranslations } from 'next-intl';
import { ArrowUpIcon } from '@heroicons/react/24/outline';
import { ScreenSize } from './types';

interface ChatInputProps {
  inputValue: string;
  onChange: (value: string) => void;
  onSend: () => void;
  disabled?: boolean;
  screenSize: ScreenSize;
}

export default function ChatInput({
  inputValue,
  onChange,
  onSend,
  disabled = false,
  screenSize
}: ChatInputProps) {
  const t = useTranslations('Chatbot');

  // Get screen size properties
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;
  
  // Adjust padding based on screen size
  let containerPadding = 'p-3';
  let inputPadding = 'px-4 py-2';
  let iconSize = 'h-5 w-5';
  let buttonSize = 'h-9 w-9';
  let buttonRight = 'right-2';
  
  if (isSmall) {
    if (width <= 320) { // Extremely small screens
      containerPadding = 'p-1.5';
      inputPadding = 'px-2 py-1';
      buttonSize = 'h-7 w-7';
      iconSize = 'h-3 w-3';
    } else if (width <= 375) { // iPhone SE size
      containerPadding = 'p-2';
      inputPadding = 'px-3 py-1.5';
      buttonSize = 'h-8 w-8';
      iconSize = 'h-4 w-4';
    } else { // Other mobile devices
      containerPadding = 'p-2.5';
      inputPadding = 'px-3 py-1.5';
      buttonSize = 'h-8 w-8';
      iconSize = 'h-4 w-4';
    }
  }
  
  return (
    <div className={`border-t border-gray-200 bg-gray-50 rounded-b-2xl ${containerPadding}`}>
      <div className="relative flex items-center">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && onSend()}
          placeholder={t('replyPlaceholder')}
          disabled={disabled}
          className={`flex-1 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-pink-500 
                     focus:border-transparent pr-12 responsive-text ${inputPadding} ${disabled ? 'bg-gray-200 cursor-not-allowed' : ''}`}
        />
        <button
          onClick={onSend}
          disabled={disabled}
          className={`absolute ${buttonRight} flex items-center justify-center ${buttonSize} bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 
                    hover:to-purple-700 text-white rounded-full transition-colors ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          aria-label="Send message"
        >
          <ArrowUpIcon className={iconSize} />
        </button>
      </div>
    </div>
  );
}
