import React, { useEffect, useRef } from 'react';
import QuickActions from './QuickActions';
import ServiceOptions from './ServiceOptions';
import MainMenu from './MainMenu';
import { Message, ScreenSize } from './types';
import Cal, { getCal<PERSON><PERSON> } from "@calcom/embed-react";

declare global {
  interface Window {
    Calendly?: {
      initInlineWidget: (options: {
        url: string;
        parentElement: HTMLElement | null;
        prefill?: Record<string, unknown>;
        utm?: Record<string, unknown>;
      }) => void;
    };
  }
}

interface ChatMessageProps {
  message: Message;
  screenSize: ScreenSize;
  onQuickAction: (message: string, intent?: string) => void;
}

// Enhanced function to format bot messages with proper spacing and structure
function formatBotMessage(text: string): React.ReactNode {
  if (!text) return text;

  // Enhanced text formatting with automatic spacing detection
  let formattedText = text;

  // Add line breaks after sentences that end with periods, exclamation marks, or question marks
  // but only if they're followed by a capital letter or "For example" (indicating a new sentence/section)
  formattedText = formattedText.replace(/([.!?])\s+([A-Z]|For example)/g, '$1\n\n$2');

  // Add line breaks before "For example," patterns
  formattedText = formattedText.replace(/\s+(For example,)/gi, '\n\n$1');

  // Add line breaks after price ranges and before new information
  formattedText = formattedText.replace(/(€\d+[,\d]*(?:\s*to\s*€\d+[,\d]*)?[.,]?)\s+([A-Z])/g, '$1\n\n$2');

  // Add line breaks before "After" patterns (like "After a free consultation")
  formattedText = formattedText.replace(/\s+(After\s+)/gi, '\n\n$1');

  // Add spacing around key pricing phrases that should stand out
  formattedText = formattedText.replace(/(landing pages start from|full websites range from|chatbot setups typically cost|chatbots typically cost)/gi, '\n\n$1');

  // Add line breaks before concluding statements
  formattedText = formattedText.replace(/\s+(After a free consultation,|You'll receive)/gi, '\n\n$1');

  // Split by double newlines to create paragraphs
  const paragraphs = formattedText.split('\n\n');

  return paragraphs.map((paragraph, pIndex) => {
    // Skip empty paragraphs
    if (!paragraph.trim()) return null;

    // Split by single newlines within paragraphs
    const lines = paragraph.split('\n');

    return (
      <div key={pIndex} className={pIndex > 0 ? 'mt-4' : ''}>
        {lines.map((line, lIndex) => {
          // Skip empty lines
          if (!line.trim()) return null;

          // Process bold formatting (**text**)
          const parts = line.split(/(\*\*.*?\*\*)/g);
          const formattedLine = parts.map((part, partIndex) => {
            if (part.startsWith('**') && part.endsWith('**')) {
              const boldText = part.slice(2, -2);
              return <strong key={partIndex} className="font-semibold">{boldText}</strong>;
            }
            return part;
          });

          return (
            <div key={lIndex} className={lIndex > 0 ? 'mt-2' : ''}>
              {formattedLine}
            </div>
          );
        })}
      </div>
    );
  }).filter(Boolean); // Remove null entries
}

export default function ChatMessage({ message, screenSize, onQuickAction }: ChatMessageProps) {
  const isUser = message.isUser;
  
  // Get screen size properties
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;
  
  // Adjust spacing and sizing based on screen size
  let messageSpacing = 'mb-6';
  let avatarSize = 'w-8 h-8';
  let messagePadding = 'p-3';
  let maxWidth = 'max-w-[85%]';
  let dotSize = 'w-2 h-2';

  if (isSmall) {
    if (width <= 320) { // Extra small screens
      messageSpacing = 'mb-3';
      avatarSize = 'w-5 h-5';
      messagePadding = 'p-1.5';
      maxWidth = 'max-w-[92%]';
      dotSize = 'w-1 h-1';
    } else if (width <= 375) { // Small screens
      messageSpacing = 'mb-4';
      avatarSize = 'w-6 h-6';
      messagePadding = 'p-2';
      maxWidth = 'max-w-[90%]';
      dotSize = 'w-1.5 h-1.5';
    } else { // Medium screens
      messageSpacing = 'mb-5';
      avatarSize = 'w-7 h-7';
      messagePadding = 'p-2.5';
      maxWidth = 'max-w-[88%]';
      dotSize = 'w-1.5 h-1.5';
    }
  }

  if (message.type === 'calendly') {
    return <CalendlyWidget screenSize={screenSize} messageSpacing={messageSpacing} />;
  }

  if (message.type === 'mainMenu') {
    return (
      <div className={`w-full animate-fade-in ${messageSpacing}`}>
        <MainMenu
          onMenuClick={onQuickAction}
          screenSize={screenSize}
        />
      </div>
    );
  }

  if (message.type === 'quickActions') {
    return (
      <div className={`w-full animate-fade-in ${messageSpacing}`}>
        <QuickActions
          onActionClick={onQuickAction}
          screenSize={screenSize}
        />
      </div>
    );
  }

  if (message.type === 'serviceOptions') {
    return (
      <div className={`w-full animate-fade-in ${messageSpacing}`}>
        <ServiceOptions
          onServiceClick={onQuickAction}
          screenSize={screenSize}
        />
      </div>
    );
  }

  return (
    <div className={`flex items-start ${isUser ? 'justify-end' : 'justify-start'} w-full ${messageSpacing}`}>
      <div className={`flex items-start space-x-2 ${isUser ? 'flex-row-reverse space-x-reverse' : ''} ${maxWidth}`}>
        {!isUser && (
          <img src="/logo/chatbot_avatar.png" alt="Bot Avatar" className={`${avatarSize} rounded-full flex-shrink-0`} />
        )}
        <div
          className={`${
            isUser
              ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-tl-xl rounded-tr-xl rounded-bl-xl shadow'
              : 'bg-gray-100 text-gray-800 rounded-tr-xl rounded-tl-xl rounded-br-xl shadow'
          } ${messagePadding} min-w-[40px] break-words animate-fade-in responsive-text`}
        >
          {message.text === '...' && !isUser ? (
            <div className="flex space-x-1">
              <span className={`${dotSize} bg-gray-400 rounded-full animate-bounce`} style={{ animationDelay: '0s' }}></span>
              <span className={`${dotSize} bg-gray-400 rounded-full animate-bounce`} style={{ animationDelay: '0.2s' }}></span>
              <span className={`${dotSize} bg-gray-400 rounded-full animate-bounce`} style={{ animationDelay: '0.4s' }}></span>
            </div>
          ) : isUser ? (
            message.text
          ) : (
            formatBotMessage(message.text)
          )}
        </div>
      </div>
    </div>
  );
}

// Calendly Widget Component
function CalendlyWidget({ screenSize, messageSpacing }: { screenSize: ScreenSize; messageSpacing: string }) {
  const calendlyRef = useRef<HTMLDivElement>(null);
  const isInitializedRef = useRef(false);
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;

  useEffect(() => {
    // Cal.com embed initialization for chatbot with proper CSS variables and error handling
    (async function () {
      try {
        console.log('🔄 Initializing Cal.com API for chatbot...');
        const cal = await getCalApi({"namespace":"30min-chat"});
        console.log('✅ Cal.com chatbot API loaded successfully');

        cal("ui", {
          "theme": "light",
          "styles": {
            "branding": {
              "brandColor": "#7c3aed"
            }
          },
          "hideEventTypeDetails": true,
          "layout": "month_view"
        });

        console.log('✅ Cal.com chatbot UI configured successfully');
      } catch (error) {
        console.error('❌ Cal.com chatbot initialization error:', error);
      }
    })();

    // Hide Cal.com branding
    const style = document.createElement('style');
    style.textContent = `
      [data-cal-namespace="30min-chat"] [data-testid="powered-by-cal"] {
        display: none !important;
      }
      [data-cal-namespace="30min-chat"] .cal-branding {
        display: none !important;
      }
      [data-cal-namespace="30min-chat"] a[href*="cal.com"] {
        display: none !important;
      }
    `;
    document.head.appendChild(style);
  }, []);

  return (
    <div className={`w-full animate-fade-in ${messageSpacing} flex justify-center`}>
      <div className="bg-gray-50 rounded-lg p-2 w-full">
        <div
          style={{
            minWidth: isSmall
              ? width <= 320 ? '220px' : '240px'
              : '280px',
            minHeight: isSmall
              ? width <= 320 ? '220px' : '250px'
              : '300px',
            height: 'auto',
            maxHeight: '500px',
            width: '100%'
          }}
        >
          <Cal
            namespace="30min-chat"
            calOrigin="https://cal.com"
            calLink="upzera/30min"
            style={{
              width: "100%",
              minHeight: isSmall
                ? width <= 320 ? '220px' : '250px'
                : '300px',
              height: 'auto',
              maxHeight: '500px',
              overflow: "scroll"
            }}
            config={{
              "layout": "month_view",
              "theme": "light"
            }}
          />
        </div>
      </div>
    </div>
  );
}
