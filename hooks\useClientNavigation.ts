"use client";

import { useRouter, usePathname } from '@/i18n/navigation';
import { useLocale } from 'next-intl';
import { useCallback, useTransition } from 'react';

export function useClientNavigation() {
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();
  const [isPending, startTransition] = useTransition();

  const navigateToLocale = useCallback((newLocale: string) => {
    if (newLocale === locale) {
      return; // Don't navigate if same locale
    }

    // Store the new locale preference immediately
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-locale', newLocale);
      sessionStorage.setItem('current-locale', newLocale);
    }

    startTransition(() => {
      // Use replace for locale changes to avoid history pollution
      // Force a hard navigation to ensure proper locale switching
      router.replace(pathname, { locale: newLocale });
    });
  }, [router, pathname, locale]);

  const navigateToPath = useCallback((path: string, options?: { locale?: string }) => {
    startTransition(() => {
      if (options?.locale) {
        router.push(path, { locale: options.locale });
      } else {
        router.push(path);
      }
    });
  }, [router]);

  return {
    navigateToLocale,
    navigateToPath,
    isPending,
    currentLocale: locale,
    currentPath: pathname
  };
}
