"use client";

import { But<PERSON> } from '@/components/ui/button';
import { Globe } from 'lucide-react';
import { useState } from 'react';
import { useClientNavigation } from '@/hooks/useClientNavigation';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const languages = [
  { code: 'en', name: 'English', flag: '🇬🇧' },
  { code: 'lt', name: 'Lietuvių', flag: '🇱🇹' },
];

interface LanguageSwitcherProps {
  keepMenuOpen?: boolean;
}

export default function LanguageSwitcher({ keepMenuOpen = false }: LanguageSwitcherProps) {
  const { navigateToLocale, isPending, currentLocale } = useClientNavigation();
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = (newLocale: string) => {
    // If the same language is clicked, do nothing to prevent scrolling to top
    if (newLocale === currentLocale) {
      return;
    }

    // Close dropdown unless we want to keep menu open
    if (!keepMenuOpen) {
      setIsOpen(false);
    }

    // If we want to keep the menu open, preserve the menu state
    if (keepMenuOpen && typeof window !== 'undefined') {
      // Ensure the menu state is preserved during navigation
      sessionStorage.setItem('mobileMenuOpen', 'true');
    }

    // Use the robust navigation hook
    navigateToLocale(newLocale);
  };

  const currentLanguage = languages.find(lang => lang.code === currentLocale);

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          className={`text-white/80 hover:text-white hover:bg-purple-800/50 transition-colors bg-transparent border-none min-h-[44px] sm:min-h-auto px-3 sm:px-4 ${
            isPending ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          disabled={isPending}
        >
          <Globe className="h-4 w-4 sm:h-4 sm:w-4 mr-2" />
          <span className="hidden sm:inline text-sm sm:text-base">{currentLanguage?.name}</span>
          <span className="sm:hidden text-lg">{currentLanguage?.flag}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="bg-purple-900 border-purple-700 min-w-[160px] sm:min-w-[140px] w-[160px] sm:w-[140px] overflow-hidden"
        sideOffset={8}
        alignOffset={0}
        avoidCollisions={true}
        collisionPadding={20}
        side="bottom"
      >
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            disabled={isPending || currentLocale === language.code}
            className={`cursor-pointer text-white hover:bg-purple-800 focus:bg-purple-800 transition-colors py-4 sm:py-3 px-4 text-base min-h-[48px] sm:min-h-auto ${
              currentLocale === language.code ? 'bg-purple-800' : ''
            } ${isPending ? 'opacity-50' : ''}`}
          >
            <span className="mr-3 text-lg sm:text-lg">{language.flag}</span>
            <span className="font-medium text-base sm:text-base">{language.name}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
