// API endpoint for scraping website content for knowledge base
import { NextApiRequest, NextApiResponse } from 'next';
import * as cheerio from 'cheerio';

interface ScrapedContent {
  url: string;
  title: string;
  content: string;
  headings: string[];
  links: string[];
  error?: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Add CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { urls } = req.body;

  if (!urls || !Array.isArray(urls)) {
    return res.status(400).json({ error: 'URLs array is required' });
  }

  try {
    const scrapedData: ScrapedContent[] = [];

    for (const url of urls) {
      try {
        console.log(`Scraping: ${url}`);
        
        const response = await fetch(url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const html = await response.text();
        const $ = cheerio.load(html);

        // Remove script and style elements
        $('script, style, nav, footer, header, .navigation, .menu').remove();

        // Extract title
        const title = $('title').text().trim() || $('h1').first().text().trim() || 'Untitled';

        // Extract main content
        let content = '';
        const contentSelectors = [
          'main',
          '[role="main"]',
          '.content',
          '.main-content',
          'article',
          '.post-content',
          '.entry-content'
        ];

        for (const selector of contentSelectors) {
          const element = $(selector).first();
          if (element.length && element.text().trim().length > 100) {
            content = element.text().trim();
            break;
          }
        }

        // If no main content found, extract from body
        if (!content) {
          content = $('body').text().trim();
        }

        // Clean content
        content = content
          .replace(/\s+/g, ' ')
          .replace(/\n+/g, ' ')
          .trim();

        // Extract headings
        const headings: string[] = [];
        $('h1, h2, h3, h4, h5, h6').each((_, element) => {
          const heading = $(element).text().trim();
          if (heading && heading.length > 3) {
            headings.push(heading);
          }
        });

        // Extract internal links
        const links: string[] = [];
        $('a[href]').each((_, element) => {
          const href = $(element).attr('href');
          if (href && (href.startsWith('/') || href.includes(new URL(url).hostname))) {
            const linkText = $(element).text().trim();
            if (linkText && linkText.length > 3) {
              links.push(`${linkText}: ${href}`);
            }
          }
        });

        scrapedData.push({
          url,
          title,
          content: content.substring(0, 5000), // Limit content length
          headings: headings.slice(0, 20), // Limit headings
          links: links.slice(0, 10) // Limit links
        });

      } catch (error) {
        console.error(`Error scraping ${url}:`, error);
        scrapedData.push({
          url,
          title: 'Error',
          content: '',
          headings: [],
          links: [],
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    res.status(200).json({ 
      success: true, 
      data: scrapedData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Scraping error:', error);
    res.status(500).json({ 
      error: 'Failed to scrape content',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
