"use client";

import { useEffect } from 'react';
import { useLocale } from 'next-intl';

/**
 * Component that ensures proper locale handling and persistence
 * Helps prevent locale switching issues between desktop and mobile
 */
export default function LocaleProvider({ children }: { children: React.ReactNode }) {
  const locale = useLocale();

  useEffect(() => {
    // Ensure the document lang attribute matches the current locale
    if (typeof document !== 'undefined') {
      document.documentElement.lang = locale;
      // Also set the dir attribute for RTL languages if needed in the future
      document.documentElement.dir = 'ltr'; // Both EN and LT are LTR
    }

    // Store the current locale in sessionStorage for persistence
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('current-locale', locale);

      // Also store in localStorage for longer persistence
      localStorage.setItem('preferred-locale', locale);
    }
  }, [locale]);

  // Handle browser back/forward navigation
  useEffect(() => {
    const handlePopState = () => {
      // Force a page reload on back/forward navigation to ensure proper locale
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname;
        const pathLocale = currentPath.split('/')[1];
        
        // If the URL locale doesn't match our stored locale, reload
        const storedLocale = sessionStorage.getItem('current-locale');
        if (storedLocale && pathLocale !== storedLocale) {
          window.location.reload();
        }
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  return <>{children}</>;
}
