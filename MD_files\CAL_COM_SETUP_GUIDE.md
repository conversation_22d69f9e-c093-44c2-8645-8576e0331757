# 🚀 Cal.com Setup Guide for UpZera Integration

## Current Status ✅
- ✅ Cal.com embed package installed (`@calcom/embed-react`)
- ✅ Contact page updated with Cal.com embed
- ✅ Chatbot updated with Cal.com embed  
- ✅ CSP headers updated to allow Cal.com domains
- ✅ Calendly code removed/commented out

## Next Steps Required

### Option 1: Quick Setup (Recommended for Testing) 🚀

**Use Cal.com's hosted service - FREE and immediate**

1. **Create Cal.com Account**
   - Go to [cal.com](https://cal.com)
   - Sign up with your email
   - Choose a username (e.g., `edgaras-upzera`)

2. **Set up your booking page**
   - Create an event type (e.g., "30 Minute Consultation")
   - Set your availability
   - Configure meeting details

3. **Update the embed links in your code**
   - Replace `bailey/15min` with `your-username/30min`
   - Files to update:
     - `project/app/contact/page.tsx` (line 763)
     - `project/components/chatbot/ChatMessage.tsx` (line 318)

### Option 2: Self-Hosted Cal.com (Production Ready) 🏗️

**Deploy your own Cal.com instance on Vercel**

#### Prerequisites:
- Supabase account (for database)
- Google Calendar API credentials
- Vercel account

#### Step 1: Database Setup
1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Get connection string from Settings > Database

2. **Update Cal.com Environment**
   ```bash
   # In cal-com/.env
   DATABASE_URL="your-supabase-connection-string"
   DATABASE_DIRECT_URL="your-supabase-connection-string"
   ```

#### Step 2: Google Calendar Integration
1. **Google Cloud Console Setup**
   - Go to [console.cloud.google.com](https://console.cloud.google.com)
   - Create/select project
   - Enable Google Calendar API
   - Create OAuth 2.0 credentials
   - Add authorized redirect URI: `https://your-cal-domain.vercel.app/api/integrations/googlecalendar/callback`

2. **Update Environment Variables**
   ```bash
   # In cal-com/.env
   GOOGLE_API_CREDENTIALS='{"web":{"client_id":"...","client_secret":"...","redirect_uris":["..."]}}'
   ```

#### Step 3: Deploy to Vercel
1. **Push Cal.com to GitHub**
   ```bash
   cd cal-com
   git init
   git add .
   git commit -m "Initial Cal.com setup"
   git remote add origin https://github.com/your-username/cal-com-upzera.git
   git push -u origin main
   ```

2. **Deploy on Vercel**
   - Connect GitHub repo to Vercel
   - Add environment variables in Vercel dashboard
   - Deploy

3. **Run Database Migrations**
   ```bash
   # After deployment, run in Vercel terminal or locally
   npm run db-migrate
   npm run db-seed
   ```

#### Step 4: Update UpZera Integration
```typescript
// Update both files with your deployed Cal.com URL
<Cal
  calOrigin="https://your-cal-domain.vercel.app"
  calLink="your-username/30min"
  style={{width:"100%",height:"700px",overflow:"scroll"}}
  config={{"theme": "dark"}}
/>
```

## Environment Variables Needed

### For Cal.com (.env):
```env
# Database
DATABASE_URL="postgresql://..."
DATABASE_DIRECT_URL="postgresql://..."

# NextAuth
NEXTAUTH_SECRET="your-secret"
NEXTAUTH_URL="https://your-cal-domain.vercel.app"

# App URLs
NEXT_PUBLIC_WEBAPP_URL="https://your-cal-domain.vercel.app"
NEXT_PUBLIC_WEBSITE_URL="https://your-cal-domain.vercel.app"

# Encryption
CALENDSO_ENCRYPTION_KEY="your-encryption-key"

# Email (using your existing Mailgun)
EMAIL_FROM="<EMAIL>"
EMAIL_SERVER_HOST="smtp.eu.mailgun.org"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-mailgun-smtp-password"

# Google Calendar
GOOGLE_API_CREDENTIALS='{"web":{...}}'
GOOGLE_LOGIN_ENABLED=false

# Branding
NEXT_PUBLIC_APP_NAME="UpZera Calendar"
NEXT_PUBLIC_SUPPORT_MAIL_ADDRESS="<EMAIL>"
NEXT_PUBLIC_COMPANY_NAME="UpZera"
NEXT_PUBLIC_DISABLE_SIGNUP=true
```

## Testing the Integration

1. **Start your UpZera dev server**
   ```bash
   cd project
   npm run dev
   ```

2. **Test the embeds**
   - Visit `/contact` page
   - Open chatbot and trigger calendar booking
   - Verify Cal.com embed loads properly

## Troubleshooting

### Common Issues:
1. **CSP Errors**: Already fixed in your `next.config.js`
2. **Invalid calLink**: Update with your actual Cal.com username
3. **CORS Issues**: Ensure Cal.com domain is whitelisted
4. **Database Connection**: Check Supabase connection string

### Current Temporary Setup:
- Using `bailey/15min` as demo booking page
- This works for testing but replace with your own booking page

## Next Actions:
1. Choose Option 1 or Option 2
2. If Option 1: Create Cal.com account and update links
3. If Option 2: Set up database and deploy to Vercel
4. Test booking functionality
5. Customize branding and event types
