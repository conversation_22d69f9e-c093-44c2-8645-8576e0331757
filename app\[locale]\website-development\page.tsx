"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { HeroBackground } from "@/components/ui/hero-background"; // Import the new component
import { Rocket, Zap, Cpu, LayoutTemplate, Search, Smartphone, Link2, CheckCircle } from "lucide-react";
import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';

export default function WebsiteDevelopment() {
  const t = useTranslations('WebsiteDevelopmentPage');
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 pt-24">
    {/* Hero Section */}
    <div className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center">
          {/* Changed leading-tight to leading-normal */}
<h1 className="text-5xl lg:text-6xl font-bold text-white leading-relaxed mb-6 overflow-visible  opacity-0 animate-fadeIn">
            <span className="block opacity-0 animate-slideInFromLeft" style={{ animationDelay: '0.3s' }}>
              {t('heroTitle1')}
            </span>
            <span className="block bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text opacity-0 pb-1.5 animate-slideInFromRight" style={{ animationDelay: '0.8s' }}>
              {t('heroTitle2')}
            </span>
          </h1>
          <p className="text-xl text-purple-100/80 mb-8 max-w-4xl mx-auto opacity-0 animate-fadeIn" style={{ animationDelay: '1.2s' }}>
            {t('heroSubtitle')}
          </p>
          <div className="opacity-0 animate-fadeIn" style={{ animationDelay: '1.5s' }}>
            <Link href="/contact?service=webdev#contact-form">
              <Button
                size="lg"
                className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 transition-all duration-200 hover:scale-105"
              >
                {t('getStarted')}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>

      {/* Benefits Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-purple-950/50">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            {t('workflowTitle')}
            <br/>
            {t('workflowSubtitle')}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Zap,
                title: t('lightningFastTitle'),
                description: t('lightningFastDesc')
              },
              {
                icon: Cpu,
                title: t('smartWorkflowsTitle'),
                description: t('smartWorkflowsDesc')
              },
              {
                icon: LayoutTemplate,
                title: t('cleanDesignTitle'),
                description: t('cleanDesignDesc')
              },
              {
                icon: Search,
                title: t('seoOptimizedTitle'),
                description: t('seoOptimizedDesc')
              },
              {
                icon: Smartphone,
                title: t('mobileFirstTitle'),
                description: t('mobileFirstDesc')
              },
              {
                icon: Link2,
                title: t('seamlessIntegrationsTitle'),
                description: t('seamlessIntegrationsDesc')
              }
            ].map((benefit, index) => (
              <div
                key={index}
                className="bg-purple-900/20 backdrop-blur-sm rounded-lg p-6 border border-purple-700/20 hover:bg-purple-900/30 transition-colors duration-200 hover:scale-105 transform"
              >
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  <benefit.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">{benefit.title}</h3>
                <p className="text-purple-100/70">{benefit.description}</p>
              </div>
            ))}
          </div>

          {/* Tech Stack Section */}
          <div className="mt-16">
            <h3 className="text-2xl font-bold text-white text-center mb-8">
              {t('techStackTitle')}
            </h3>
            <p className="text-center text-purple-100/70 mb-10 max-w-3xl mx-auto">
              {t('techStackDesc')}
            </p>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-8 max-w-5xl mx-auto">
              {[
                { name: "JavaScript", logo: "/logo/tech-stack/javascript.svg" },
                { name: "Node.js", logo: "/logo/tech-stack/nodejs.svg" },
                { name: "TypeScript", logo: "/logo/tech-stack/typescript.svg" },
                { name: "Vue.js", logo: "/logo/tech-stack/vuejs.svg" },
                { name: "Python", logo: "/logo/tech-stack/python.svg" },
                { name: "Astro", logo: "/logo/tech-stack/astro.svg" },
                { name: "React / React Native", logo: "/logo/tech-stack/react.svg" },
                { name: "Next.js", logo: "/logo/tech-stack/nextjs.svg" },
                { name: "Angular", logo: "/logo/tech-stack/angular.svg" },
                { name: "Supabase", logo: "/logo/tech-stack/supabase.svg" },
                { name: "Svelte", logo: "/logo/tech-stack/svelte.svg" },
                { name: "Gatsby", logo: "/logo/tech-stack/gatsby.svg" }
              ].map((tech, index) => (
                <div
                  key={index}
                  className="flex flex-col items-center justify-center p-4 bg-purple-900/20 backdrop-blur-sm rounded-lg border border-purple-700/20 hover:bg-purple-900/30 transition-all duration-200 hover:scale-105 transform"
                >
                  <div className="w-12 h-12 mb-3 flex items-center justify-center">
                    <img
                      src={tech.logo}
                      alt={`${tech.name} logo`}
                      className="max-w-full max-h-full"
                    />
                  </div>
                  <span className="text-purple-100 text-sm font-medium text-center">{tech.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Packages Section - Reverting HeroBackground to wrap the whole section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-white"> {/* Removed HeroBackground, plain div */}
        <div className="relative max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-purple-950 text-center mb-6">
            {t('packagesTitle')}{" "}
            <span className="bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">
              {t('packagesHighlight')}
            </span>
          </h2>
          <p className="text-center text-purple-600/80 mb-10 max-w-3xl mx-auto text-sm">
            {t('packagesSubtitle')}
          </p>
          {/* Grid itself doesn't need HeroBackground now */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Package 1: Starter */}
            <Card className="bg-white border-2 border-purple-200 hover:border-purple-500 text-purple-950 flex flex-col hover:shadow-xl transition-all duration-300 h-full hover:scale-105">
              <CardHeader>
                <CardTitle className="text-2xl font-semibold bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">
                  {t('starterTitle')}
                </CardTitle>

                <p className="text-purple-600 text-sm mt-2">
                  {t('starterDesc')}
                </p>
              </CardHeader>
              <CardContent className="flex-grow flex flex-col">
                <ul className="space-y-3 text-purple-800 mb-6 flex-grow">
                  
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('starterFeature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('starterFeature3')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('starterFeature4')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('starterFeature5')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('starterFeature6')}</span>
                  </li>
                </ul>
                <Link href="/contact?service=webdev-starter" className="mt-auto">
                  <Button className="w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-[1.02]">
                    {t('getQuote')}
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Package 2: Professional */}
            <Card className="bg-white border-2 border-purple-200 hover:border-purple-500 text-purple-950 shadow-xl shadow-purple-500/20 flex flex-col hover:shadow-2xl transition-all duration-300 h-full hover:scale-105">
              <CardHeader>
                <CardTitle className="text-2xl font-semibold bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">
                  {t('professionalTitle')}
                </CardTitle>

                <p className="text-purple-600 text-sm mt-2">
                  {t('professionalDesc')}
                </p>
              </CardHeader>
              <CardContent className="flex-grow flex flex-col">
                <ul className="space-y-3 text-purple-800 mb-6 flex-grow">
                  
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('professionalFeature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('professionalFeature3')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('professionalFeature4')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('professionalFeature5')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('professionalFeature6')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('professionalFeature7')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('professionalFeature8')}</span>
                  </li>
                </ul>
                <Link href="/contact?service=webdev-professional" className="mt-auto">
                  <Button className="w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-[1.02]">
                    {t('getQuote')}
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Package 3: Enterprise */}
            <Card className="bg-white border-2 border-purple-200 hover:border-purple-500 text-purple-950 flex flex-col hover:shadow-xl transition-all duration-300 h-full hover:scale-105">
              <CardHeader>
                <CardTitle className="text-2xl font-semibold bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">
                  {t('enterpriseTitle')}
                </CardTitle>

                <p className="text-purple-600 text-sm mt-2">
                  {t('enterpriseDesc')}
                </p>
              </CardHeader>
              <CardContent className="flex-grow flex flex-col">
                <ul className="space-y-3 text-purple-800 mb-6 flex-grow">
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('enterpriseFeature2')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('enterpriseFeature3')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('enterpriseFeature4')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('enterpriseFeature5')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('enterpriseFeature6')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('enterpriseFeature7')}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>{t('enterpriseFeature8')}</span>
                  </li>
                </ul>
                <Link href="/contact?service=webdev-enterprise" className="mt-auto">
                  <Button className="w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-[1.02]">
                    {t('getQuote')}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div> {/* Close plain div wrapper for the section */}

      {/* Enhanced All Projects Include Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-purple-800 to-purple-900 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full opacity-5 pointer-events-none">
          <div className="absolute top-10 left-10 w-64 h-64 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-64 h-64 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 blur-3xl"></div>
        </div>

        <div className="max-w-5xl mx-auto text-center relative z-10">
           <h2 className="text-4xl font-bold text-white mb-12">
            {t('whyChooseTitle')}
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-x-12 md:gap-y-8 text-left max-w-6xl mx-auto px-4">
             {[
              t('feature1'),
              t('feature2'),
              t('feature3'),
              t('feature4'),
              t('feature5'),
              t('feature6')
            ].map((feature, index) => (
              <div
                key={index}
                className="group flex items-center p-4 rounded-xl border border-purple-700/30 backdrop-blur-sm bg-purple-900/20 hover:bg-purple-900/40 hover:border-pink-500/50 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/10"
              >
                <div className="flex-shrink-0 mr-4 relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300"></div>
                  <div className="relative bg-gradient-to-br from-pink-500 to-purple-500 w-10 h-10 rounded-full flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300 shadow-md shadow-purple-500/20">
                    <CheckCircle className="w-5 h-5 text-white" />
                  </div>
                </div>
                <span className="text-lg text-purple-100/90 group-hover:text-white transition-colors duration-300">{feature}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* New CTA Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-pink-500 to-purple-500">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            {t('ctaTitle')}
          </h2>
          <p className="text-xl text-purple-100/80 mb-8">
            {t('ctaSubtitle')}
          </p>
          <Link href="/contact?service=webdev">
            <Button
              size="lg"
              className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 transition-all duration-200 hover:scale-105"
            >
              {t('ctaButton')}
            </Button>
          </Link>
        </div>
      </div>

    </div>
  );
}
