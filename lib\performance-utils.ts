import { useCallback, useEffect, useRef, useState } from 'react';

/**
 * Debounce function that delays execution until after wait milliseconds have elapsed
 * since the last time the debounced function was invoked.
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): T & { cancel: () => void } {
  let timeout: NodeJS.Timeout | null = null;
  
  const debounced = function (this: any, ...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func.apply(this, args);
  } as T & { cancel: () => void };
  
  debounced.cancel = () => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
  };
  
  return debounced;
}

/**
 * Throttle function that ensures the function is called at most once per specified interval
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): T & { cancel: () => void } {
  let inThrottle: boolean = false;
  let timeout: NodeJS.Timeout | null = null;
  
  const throttled = function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      timeout = setTimeout(() => {
        inThrottle = false;
        timeout = null;
      }, limit);
    }
  } as T & { cancel: () => void };
  
  throttled.cancel = () => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    inThrottle = false;
  };
  
  return throttled;
}

/**
 * Hook for debounced event handlers
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T & { cancel: () => void } {
  const callbackRef = useRef(callback);
  const debouncedRef = useRef<(T & { cancel: () => void }) | null>(null);
  
  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback, ...deps]);
  
  // Create debounced function
  useEffect(() => {
    if (debouncedRef.current) {
      debouncedRef.current.cancel();
    }
    
    debouncedRef.current = debounce(
      (...args: Parameters<T>) => callbackRef.current(...args),
      delay
    ) as T & { cancel: () => void };
    
    return () => {
      if (debouncedRef.current) {
        debouncedRef.current.cancel();
      }
    };
  }, [delay, ...deps]);
  
  return debouncedRef.current!;
}

/**
 * Hook for throttled event handlers
 */
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  limit: number,
  deps: React.DependencyList = []
): T & { cancel: () => void } {
  const callbackRef = useRef(callback);
  const throttledRef = useRef<(T & { cancel: () => void }) | null>(null);
  
  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback, ...deps]);
  
  // Create throttled function
  useEffect(() => {
    if (throttledRef.current) {
      throttledRef.current.cancel();
    }
    
    throttledRef.current = throttle(
      (...args: Parameters<T>) => callbackRef.current(...args),
      limit
    ) as T & { cancel: () => void };
    
    return () => {
      if (throttledRef.current) {
        throttledRef.current.cancel();
      }
    };
  }, [limit, ...deps]);
  
  return throttledRef.current!;
}

/**
 * Detects if a resize event is likely due to zoom vs actual window resize
 */
export function isZoomResize(
  previousDimensions: { width: number; height: number },
  currentDimensions: { width: number; height: number },
  threshold: number = 0.1
): boolean {
  const widthRatio = currentDimensions.width / previousDimensions.width;
  const heightRatio = currentDimensions.height / previousDimensions.height;
  
  // If both dimensions change proportionally, it's likely a zoom
  const ratiosDiff = Math.abs(widthRatio - heightRatio);
  return ratiosDiff < threshold;
}

/**
 * Hook for viewport change detection with zoom awareness
 */
export function useViewportChange(
  callback: (dimensions: { width: number; height: number; isZoom: boolean }) => void,
  debounceMs: number = 150
) {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const previousDimensionsRef = useRef({ width: 0, height: 0 });
  const callbackRef = useRef(callback);
  const debouncedRef = useRef<((dimensions: { width: number; height: number; isZoom: boolean }) => void) & { cancel: () => void } | null>(null);

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // Create debounced function
  useEffect(() => {
    if (debouncedRef.current) {
      debouncedRef.current.cancel();
    }

    debouncedRef.current = debounce(
      (dimensions: { width: number; height: number; isZoom: boolean }) => callbackRef.current(dimensions),
      debounceMs
    ) as ((dimensions: { width: number; height: number; isZoom: boolean }) => void) & { cancel: () => void };

    return () => {
      if (debouncedRef.current) {
        debouncedRef.current.cancel();
      }
    };
  }, [debounceMs]);

  useEffect(() => {
    if (!debouncedRef.current) return;

    const handleResize = () => {
      const newDimensions = {
        width: window.innerWidth,
        height: window.innerHeight
      };

      const isZoom = previousDimensionsRef.current.width > 0 &&
                    isZoomResize(previousDimensionsRef.current, newDimensions);

      setDimensions(newDimensions);

      if (debouncedRef.current) {
        debouncedRef.current({ ...newDimensions, isZoom });
      }

      previousDimensionsRef.current = newDimensions;
    };

    // Set initial dimensions
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      if (debouncedRef.current) {
        debouncedRef.current.cancel();
      }
    };
  }, []);

  return dimensions;
}

/**
 * Hook for optimized scroll handling
 */
export function useOptimizedScroll(
  callback: (scrollY: number) => void,
  throttleMs: number = 16 // ~60fps
) {
  const callbackRef = useRef(callback);
  const throttledRef = useRef<((scrollY: number) => void) & { cancel: () => void } | null>(null);

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // Create throttled function
  useEffect(() => {
    if (throttledRef.current) {
      throttledRef.current.cancel();
    }

    throttledRef.current = throttle(
      (scrollY: number) => callbackRef.current(scrollY),
      throttleMs
    ) as ((scrollY: number) => void) & { cancel: () => void };

    return () => {
      if (throttledRef.current) {
        throttledRef.current.cancel();
      }
    };
  }, [throttleMs]);

  useEffect(() => {
    if (!throttledRef.current) return;

    const handleScroll = () => {
      if (throttledRef.current) {
        throttledRef.current(window.scrollY);
      }
    };

    // Set initial scroll position
    handleScroll();

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (throttledRef.current) {
        throttledRef.current.cancel();
      }
    };
  }, []);
}

/**
 * Performance monitoring utilities
 */
export class PerformanceTracker {
  private static instance: PerformanceTracker;
  private metrics: Map<string, number[]> = new Map();
  
  static getInstance(): PerformanceTracker {
    if (!PerformanceTracker.instance) {
      PerformanceTracker.instance = new PerformanceTracker();
    }
    return PerformanceTracker.instance;
  }
  
  startMeasure(name: string): void {
    performance.mark(`${name}-start`);
  }
  
  endMeasure(name: string): number {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    
    const measure = performance.getEntriesByName(name, 'measure')[0];
    const duration = measure.duration;
    
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(duration);
    
    // Clean up marks
    performance.clearMarks(`${name}-start`);
    performance.clearMarks(`${name}-end`);
    performance.clearMeasures(name);
    
    return duration;
  }
  
  getAverageTime(name: string): number {
    const times = this.metrics.get(name) || [];
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }
  
  getMetrics(): Record<string, { average: number; count: number; latest: number }> {
    const result: Record<string, { average: number; count: number; latest: number }> = {};

    this.metrics.forEach((times, name) => {
      result[name] = {
        average: this.getAverageTime(name),
        count: times.length,
        latest: times[times.length - 1] || 0
      };
    });

    return result;
  }
  
  clearMetrics(): void {
    this.metrics.clear();
  }
}

/**
 * Hook for measuring component render performance
 */
export function useRenderPerformance(componentName: string) {
  const tracker = PerformanceTracker.getInstance();
  
  useEffect(() => {
    tracker.startMeasure(`${componentName}-render`);
    return () => {
      tracker.endMeasure(`${componentName}-render`);
    };
  });
}
