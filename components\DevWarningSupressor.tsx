"use client";

import { useEffect } from 'react';

/**
 * Development-only component to suppress non-critical warnings
 * This helps clean up the console during development while preserving important errors
 */
export default function DevWarningSupressor() {
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    // Store original console methods
    const originalWarn = console.warn;
    const originalError = console.error;

    // Patterns of warnings to suppress (these are known to be safe)
    const suppressedPatterns = [
      /markdownToSafeHTML should not be imported on the client side/,
      /createWithEqualityFn/,
      /Existing embed CSS vars are being reset/,
      /react-i18next.*deprecated/i,
      /useStore.*deprecated/i,
      /Cal\.com.*CSS.*reset/i,
    ];

    // Override console.warn
    console.warn = (...args: any[]) => {
      const message = args.join(' ');
      
      const shouldSuppress = suppressedPatterns.some(pattern => 
        pattern.test(message)
      );
      
      if (!shouldSuppress) {
        originalWarn.apply(console, args);
      }
    };

    // Override console.error for specific non-critical error patterns
    console.error = (...args: any[]) => {
      const message = args.join(' ');
      
      // Only suppress very specific non-critical errors
      const shouldSuppress = suppressedPatterns.some(pattern => 
        pattern.test(message)
      );
      
      if (!shouldSuppress) {
        originalError.apply(console, args);
      }
    };

    // Cleanup function to restore original console methods
    return () => {
      console.warn = originalWarn;
      console.error = originalError;
    };
  }, []);

  // This component doesn't render anything
  return null;
}
