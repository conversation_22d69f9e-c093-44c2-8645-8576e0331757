# 🚀 Cal.com Integration Plan - Complete Calendly Replacement
## Free Self-Hosted Solution using Vercel + Supabase

---

## 📋 Project Overview

**Goal:** Replace Calendly with self-hosted Cal.com integration, fully branded and embedded within UpZera platform.

**Benefits:**
- ✅ No monthly fees (save €30/month from Calendly)
- ✅ No "Powered by" branding
- ✅ Full control and customization
- ✅ Advanced features (team scheduling, multiple calendars, etc.)
- ✅ Production-ready solution with active community support

**Tech Stack:**
- **Hosting:** Vercel (Free Tier)
- **Database:** Supabase (existing)
- **Calendar API:** Google Calendar (existing credentials)
- **Frontend:** Cal.com embed components

---

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   UpZera Site   │    │   Cal.com App    │    │   Supabase DB   │
│  (upzera.com)   │◄──►│ (cal.upzera.com) │◄──►│   (existing)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                        │                        │
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Cal.com Embeds  │    │ Google Calendar  │    │ Email Notifications│
│   (widgets)     │    │      API         │    │   (existing)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

---

## 📅 Implementation Timeline

**Total Estimated Time:** 2-3 days
- **Phase 1:** Setup & Configuration (4-6 hours)
- **Phase 2:** Integration & Customization (6-8 hours)
- **Phase 3:** Testing & Deployment (2-4 hours)

---

## 🎯 Phase 1: Cal.com Setup & Deployment

### Task 1.1: Fork and Prepare Cal.com Repository
**Estimated Time:** 30 minutes

**Steps:**
1. Fork Cal.com repository: https://github.com/calcom/cal.com
2. Clone your fork locally:
   ```bash
   git clone https://github.com/YOUR_USERNAME/cal.com.git
   cd cal.com
   ```
3. Install dependencies:
   ```bash
   yarn install
   ```

**Deliverables:**
- [ ] Forked Cal.com repository
- [ ] Local development environment ready

### Task 1.2: Environment Configuration
**Estimated Time:** 45 minutes

**Steps:**
1. Copy environment template:
   ```bash
   cp .env.example .env
   cp .env.appStore.example .env.appStore
   ```

2. Configure key environment variables in `.env`:
   ```env
   # Database (use your existing Supabase)
   DATABASE_URL="postgresql://postgres:[password]@[host]:5432/postgres"
   
   # NextAuth configuration
   NEXTAUTH_SECRET="your-generated-secret"
   NEXTAUTH_URL="https://cal-upzera.vercel.app"
   
   # Google Calendar API (reuse existing credentials)
   GOOGLE_API_CREDENTIALS='{"client_id":"...","client_secret":"..."}'
   GOOGLE_LOGIN_ENABLED=false
   
   # Email configuration (reuse existing)
   EMAIL_FROM="<EMAIL>"
   EMAIL_SERVER_HOST="smtp.gmail.com"
   EMAIL_SERVER_PORT=587
   EMAIL_SERVER_USER="your-email"
   EMAIL_SERVER_PASSWORD="your-app-password"
   
   # App configuration
   NEXT_PUBLIC_WEBAPP_URL="https://cal-upzera.vercel.app"
   NEXT_PUBLIC_WEBSITE_URL="https://cal-upzera.vercel.app"
   ```

3. Generate NextAuth secret:
   ```bash
   openssl rand -base64 32
   ```

**Deliverables:**
- [ ] Configured `.env` file
- [ ] Configured `.env.appStore` file
- [ ] Generated secure NextAuth secret

### Task 1.3: Database Setup
**Estimated Time:** 30 minutes

**Steps:**
1. Run database migrations:
   ```bash
   yarn db-migrate
   ```
2. Seed the app store:
   ```bash
   yarn db-seed
   ```
3. Verify database connection and tables created

**Deliverables:**
- [ ] Database schema migrated
- [ ] App store seeded with integrations
- [ ] Database connection verified

### Task 1.4: Vercel Deployment
**Estimated Time:** 45 minutes

**Steps:**
1. Connect GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy and verify deployment success
4. Set up custom domain (optional): `cal.upzera.com`

**Deliverables:**
- [ ] Cal.com deployed on Vercel
- [ ] Environment variables configured
- [ ] Custom domain configured (optional)
- [ ] Deployment URL accessible

---

## 🎨 Phase 2: Customization & Integration

### Task 2.1: Google Calendar Integration
**Estimated Time:** 1 hour

**Steps:**
1. Update Google Cloud Console OAuth settings:
   - Add redirect URI: `https://cal-upzera.vercel.app/api/integrations/googlecalendar/callback`
   - Add authorized domain: `cal-upzera.vercel.app`

2. Test Google Calendar connection:
   - Visit: `https://cal-upzera.vercel.app/apps/google-calendar`
   - Complete OAuth flow
   - Verify calendar sync

**Deliverables:**
- [ ] Google Calendar OAuth configured
- [ ] Calendar integration tested and working
- [ ] Events syncing properly

### Task 2.2: Branding Customization
**Estimated Time:** 2 hours

**Steps:**
1. Replace default logos and branding:
   ```bash
   # Replace in public/ directory
   - cal-logo.svg → upzera-logo.svg
   - favicon.ico → upzera-favicon.ico
   ```

2. Customize theme colors in Cal.com admin:
   - Primary color: #7c3aed (UpZera purple)
   - Secondary colors to match UpZera brand

3. Configure event types:
   - Create "30 Minute Consultation" event type
   - Set availability hours
   - Configure booking settings

**Deliverables:**
- [ ] UpZera branding applied
- [ ] Custom theme colors configured
- [ ] Event types created and configured

### Task 2.3: Remove Calendly Integration
**Estimated Time:** 1 hour

**Steps:**
1. Comment out Calendly code in `app/contact/page.tsx`:
   - Comment out Calendly script loading
   - Comment out widget initialization
   - Add placeholder for Cal.com embed

2. Comment out Calendly in chatbot (`components/chatbot/ChatMessage.tsx`):
   - Comment out Calendly widget code
   - Add placeholder for Cal.com widget

**Deliverables:**
- [ ] Calendly integration commented out
- [ ] Placeholders ready for Cal.com embeds
- [ ] No Calendly scripts loading

---

## 🔗 Phase 3: Cal.com Embed Integration

### Task 3.1: Install Cal.com Embed Package
**Estimated Time:** 15 minutes

**Steps:**
1. Install embed package in UpZera project:
   ```bash
   npm install @calcom/embed-react
   ```

**Recommended Embed:** React (iframe) - Most stable and feature-complete option

**Deliverables:**
- [ ] Cal.com embed package installed
- [ ] Package.json updated

### Task 3.2: Contact Page Integration
**Estimated Time:** 1.5 hours

**Steps:**
1. Replace Calendly embed with Cal.com embed in `app/contact/page.tsx`
2. Configure Cal.com embed with UpZera styling
3. Test responsive design and functionality

**Code Template (React iframe embed):**
```tsx
import Cal, { getCalApi } from "@calcom/embed-react";
import { useEffect } from "react";

export default function MyApp() {
  useEffect(() => {
    (async function () {
      const cal = await getCalApi({"namespace":"30min"});
      cal("ui", {
        "hideEventTypeDetails": false,
        "layout": "week_view"
      });
    })();
  }, []);

  return (
    <Cal
      namespace="30min"
      calLink="your-username/30min"
      style={{width:"100%",height:"700px",overflow:"scroll"}}
      config={{"layout":"week_view"}}
    />
  );
}
```

**Alternative Inline Embed (for smaller spaces like chatbot):**
```tsx
import { getCalApi } from "@calcom/embed-react";
import { useEffect } from "react";

export default function InlineEmbed() {
  useEffect(() => {
    (async function () {
      const cal = await getCalApi();
      cal("inline", {
        elementOrSelector: "#my-cal-inline",
        calLink: "your-username/30min",
        layout: "week_view"
      });
      cal("ui", {"hideEventTypeDetails":false,"layout":"week_view"});
    })();
  }, []);

  return <div id="my-cal-inline"></div>;
}
```

**Deliverables:**
- [ ] Cal.com React iframe embed integrated in contact page
- [ ] Styling matches UpZera theme
- [ ] Responsive design working

### Task 3.3: Chatbot Integration
**Estimated Time:** 1 hour

**Steps:**
1. Replace Calendly widget in chatbot with Cal.com inline embed
2. Optimize for smaller chatbot interface using inline embed
3. Test booking flow from chatbot

**Recommended for Chatbot:** Use inline embed for better space optimization

**Code Template for Chatbot:**
```tsx
// For chatbot - use inline embed with compact layout
useEffect(() => {
  (async function () {
    const cal = await getCalApi();
    cal("inline", {
      elementOrSelector: "#chatbot-cal",
      calLink: "your-username/30min",
      layout: "week_view"
    });
    cal("ui", {
      "hideEventTypeDetails": true,
      "layout": "week_view",
      "theme": "dark"
    });
  })();
}, []);

return <div id="chatbot-cal" style={{width:"100%",height:"400px"}}></div>;
```

**Deliverables:**
- [ ] Cal.com inline embed integrated in chatbot
- [ ] Optimized for chatbot interface
- [ ] Booking flow tested

---

## 🔧 Phase 4: Advanced Integration

### Task 4.1: Webhook Configuration
**Estimated Time:** 2 hours

**Steps:**
1. Create webhook endpoint in UpZera: `/api/webhooks/calcom`
2. Configure Cal.com to send booking events to UpZera
3. Map Cal.com booking data to existing Supabase schema
4. Test webhook integration

**Code Template:**
```typescript
// /api/webhooks/calcom/route.ts
export async function POST(request: Request) {
  const booking = await request.json();
  
  // Save to existing Supabase tables
  await supabase.from('bookings').insert({
    name: booking.attendees[0].name,
    email: booking.attendees[0].email,
    date: booking.startTime,
    // Map other fields as needed
  });
}
```

**Deliverables:**
- [ ] Webhook endpoint created
- [ ] Cal.com webhook configured
- [ ] Booking data syncing to Supabase
- [ ] Integration tested

### Task 4.2: Email Notification Integration
**Estimated Time:** 1 hour

**Steps:**
1. Configure Cal.com to use existing email templates
2. Test booking confirmation emails
3. Verify email styling matches UpZera brand

**Deliverables:**
- [ ] Email notifications working
- [ ] Email templates customized
- [ ] Branding consistent

---

## 🧪 Phase 5: Testing & Deployment

### Task 5.1: Comprehensive Testing
**Estimated Time:** 2 hours

**Test Cases:**
- [ ] Book appointment from contact page
- [ ] Book appointment from chatbot
- [ ] Receive confirmation email
- [ ] Calendar event created in Google Calendar
- [ ] Webhook data saved to Supabase
- [ ] Reschedule/cancel functionality
- [ ] Mobile responsiveness
- [ ] Different browsers compatibility

### Task 5.2: Production Deployment
**Estimated Time:** 30 minutes

**Steps:**
1. Deploy Cal.com to production Vercel environment
2. Update UpZera production with Cal.com embeds
3. Monitor for any issues
4. Update DNS if using custom domain

**Deliverables:**
- [ ] Cal.com production deployment
- [ ] UpZera production updated
- [ ] All functionality verified in production

---

## 📚 Resources & Documentation

### Key URLs:
- **Cal.com GitHub:** https://github.com/calcom/cal.com
- **Cal.com Docs:** https://cal.com/docs
- **Embed Documentation:** https://cal.com/docs/platform/embed
- **Vercel Deployment:** https://vercel.com/docs

### Support:
- **Cal.com Community:** https://github.com/calcom/cal.com/discussions
- **Vercel Support:** https://vercel.com/support

---

## ✅ Success Criteria

**Project Complete When:**
- [ ] Calendly completely removed from UpZera
- [ ] Cal.com fully functional on contact page and chatbot
- [ ] Google Calendar integration working
- [ ] Booking data syncing to Supabase
- [ ] Email notifications working
- [ ] No monthly fees or external branding
- [ ] All tests passing in production

**Estimated Total Cost:** $0/month (Vercel free tier + existing Supabase)
**Estimated Savings:** €30/month (Calendly subscription)

---

## 🚀 Quick Start Commands

### Initial Setup:
```bash
# 1. Fork and clone Cal.com
git clone https://github.com/YOUR_USERNAME/cal.com.git
cd cal.com
yarn install

# 2. Configure environment
cp .env.example .env
# Edit .env with your settings

# 3. Setup database
yarn db-migrate
yarn db-seed

# 4. Test locally
yarn dev
```

### UpZera Integration:
```bash
# In your UpZera project
npm install @calcom/embed-react

# Then replace Calendly components with Cal.com embeds
```

### Deployment:
```bash
# Connect to Vercel and deploy
# Configure environment variables in Vercel dashboard
# Deploy and test
```

---

## 💡 Pro Tips

1. **Use React (iframe) embed for main pages** - Most reliable and feature-complete
2. **Use inline embed for compact spaces** - Better for chatbot and modals
3. **Configure namespace** - Prevents conflicts when using multiple embeds
4. **Start with local development** - Test everything locally before deploying
5. **Use existing Supabase** - No need for separate Cal.com database
6. **Gradual migration** - Comment out Calendly first, then add Cal.com
7. **Test thoroughly** - Booking flow is critical for business
8. **Monitor costs** - Vercel free tier is generous but monitor usage

---

## 🔧 Troubleshooting

### Common Issues:
- **Database connection errors:** Check Supabase connection string
- **OAuth redirect errors:** Verify Google Cloud Console settings
- **Embed not loading:** Check CORS and domain settings
- **Styling issues:** Use Cal.com's theme customization options

### Getting Help:
- Cal.com GitHub Issues: https://github.com/calcom/cal.com/issues
- Cal.com Discord: https://discord.gg/calcom
- Vercel Support: https://vercel.com/support
