import {redirect} from 'next/navigation';
import type { Metadata } from 'next';

// Add proper SEO metadata for the root page that matches existing brand messaging
export const metadata: Metadata = {
  title: 'UpZera - Web Development & Intelligent Chatbot Solutions',
  description: 'Custom websites and smart chatbots that grow your business. Fast, professional, and designed for results. From landing pages to lead generation - we build digital solutions that work.',
  keywords: 'web development, chatbot solutions, digital solutions, business automation, website design, lead generation, smart automation, Netherlands, startup solutions, custom websites',
  authors: [{ name: 'UpZera Team' }],
  creator: 'UpZera',
  publisher: 'UpZera',
  metadataBase: new URL(process.env.NODE_ENV === 'production' ? 'https://upzera.com' : 'http://localhost:3000'),
  alternates: {
    canonical: '/',
    languages: {
      'en': '/en',
      'lt': '/lt',
    },
  },
  openGraph: {
    title: 'UpZera - Web Development & Intelligent Chatbot Solutions',
    description: 'Custom websites and smart chatbots that grow your business. Fast, professional, and designed for results. From landing pages to lead generation - we build digital solutions that work.',
    url: '/',
    siteName: 'UpZera',
    images: [
      {
        url: '/logo/upzera-og-image.png',
        width: 1200,
        height: 630,
        alt: 'UpZera',
      },
    ],
    locale: 'en',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'UpZera - Web Development & Intelligent Chatbot Solutions',
    description: 'Custom websites and smart chatbots that grow your business. Fast, professional, and designed for results. From landing pages to lead generation - we build digital solutions that work.',
    images: ['/logo/upzera-og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicons/favicon.ico' },
      { url: '/favicons/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicons/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/favicons/apple-touch-icon.png', sizes: '180x180' },
    ],
    other: [
      { url: '/favicons/android-chrome-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/favicons/android-chrome-512x512.png', sizes: '512x512', type: 'image/png' },
    ],
  },
  manifest: '/favicons/site.webmanifest',
  other: {
    'theme-color': '#581c87',
    'msapplication-navbutton-color': '#581c87',
    'apple-mobile-web-app-status-bar-style': 'black-translucent',
  },
};

// This page only renders when the user is on the root page
// and will redirect to the default locale
export default function RootPage() {
  redirect('/en');
}
