"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown, Mail } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

// Define FaqItemProps interface (assuming it's already defined correctly from previous steps)
// interface FaqItemProps {
//   question: string;
//   answer: string;
//   index: number;
// }

// Component to render HTML string with overflow handling
const RenderHtmlString = ({ htmlString }: { htmlString: string }) => {
  // Apply styles to prevent overflow and ensure proper rendering
  return <div
    className="w-full overflow-x-hidden break-words"
    dangerouslySetInnerHTML={{ __html: htmlString }}
  />;
};

interface FaqSectionProps extends React.HTMLAttributes<HTMLElement> {
  title: string;
  description?: string;
  items: { // Ensure the type definition for the array elements allows string | string[]
    question: string;
    answer: string; // Revert to expecting only string
  }[];
  contactInfo?: {
    title: string;
    description: string;
    buttonText: string;
    onContact?: () => void;
  };
}

const FaqSection = React.forwardRef<HTMLElement, FaqSectionProps>(
  ({ className, title, description, items, contactInfo, ...props }, ref) => {
    return (
      <section
        ref={ref}
        className={cn(
          "py-20 w-full",
          className
        )}
        {...props}
      >
        <div className="container mx-auto px-4 overflow-hidden">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-3xl mx-auto text-center mb-16"
          >
            <h2 className="text-5xl font-semibold mb-4 text-white">
              {title}
            </h2>
            {description && (
              <p className="text-lg text-white/70">{description}</p>
            )}
          </motion.div>

          {/* FAQ Items */}
          <div className="max-w-3xl mx-auto space-y-6 w-full overflow-hidden">
            {items.map((item, index) => (
              <FaqItem
                key={index}
                question={item.question}
                answer={item.answer}
                index={index}
              />
            ))}
          </div>

          {/* Contact Section */}
          {contactInfo && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="max-w-md mx-auto mt-12 p-6 rounded-lg text-center"
            >
              <div className="inline-flex items-center justify-center p-2 rounded-full mb-5">
                <Mail className="h-6 w-6 text-white" />
              </div>
              <p className="text-lg font-medium text-white mb-2">
                {contactInfo.title}
              </p>
              <p className="text-base text-white/70 mb-5">
                {contactInfo.description}
              </p>
              <Button
                onClick={contactInfo.onContact}
                className="bg-pink-600 hover:bg-pink-500 text-white px-6 py-3 h-auto text-lg"
              >
                {contactInfo.buttonText}
              </Button>
            </motion.div>
          )}
        </div>
      </section>
    );
  }
);
FaqSection.displayName = "FaqSection";

// Define FaqItemProps interface for clarity and type safety
interface FaqItemProps {
  question: string;
  answer: string; // Revert to expecting only string
  index: number;
}

// Internal FaqItem component using the defined interface
const FaqItem = React.forwardRef<HTMLDivElement, FaqItemProps>(
  (props, ref) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const { question, answer, index } = props;

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, delay: index * 0.1 }}
      className={cn(
        "group rounded-lg w-full overflow-hidden",
        "transition-all duration-200 ease-in-out",
        "border border-purple-800/40",
        isOpen
          ? "bg-purple-900/80 backdrop-blur-sm"
          : "bg-purple-900/50 hover:bg-purple-900/70"
      )}
    >
      <Button
        variant="ghost"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-5 sm:px-8 py-6 h-auto hover:bg-transparent text-white block relative"
        aria-expanded={isOpen}
        aria-controls={`faq-answer-${index}`}
        aria-label={`Toggle FAQ: ${question}`}
      >
        <div className="w-full pr-12 faq-question-container">
          <h3
            className={cn(
              "text-xl font-medium transition-colors duration-200 text-left",
              "text-white/80 break-words hyphens-auto whitespace-normal",
              isOpen && "text-white"
            )}
          >
            {question}
          </h3>
        </div>
        <motion.div
          animate={{
            rotate: isOpen ? 180 : 0,
            scale: isOpen ? 1.1 : 1,
          }}
          transition={{ duration: 0.2 }}
          className={cn(
            "p-1 rounded-full absolute right-5 top-6 faq-chevron-container",
            "transition-colors duration-200",
            isOpen ? "text-pink-500" : "text-white/70"
          )}
        >
          <ChevronDown className="h-7 w-7" />
        </motion.div>
      </Button>
      <AnimatePresence initial={false}>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{
              height: "auto",
              opacity: 1,
              transition: { duration: 0.2, ease: "easeOut" },
            }}
            exit={{
              height: 0,
              opacity: 0,
              transition: { duration: 0.2, ease: "easeIn" },
            }}
          >
            <div className="px-8 pb-6 pt-3 w-full overflow-hidden" id={`faq-answer-${index}`}>
                {/* Use the new RenderHtmlString component, wrapped in the styled div */}
                <div className="text-lg text-white/70 leading-relaxed faq-answer-content max-w-full overflow-hidden">
                   <RenderHtmlString htmlString={answer} />
                </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
});
FaqItem.displayName = "FaqItem";

export { FaqSection };