"use client";

import { useEffect, useState, useRef } from "react";
import { PerformanceTracker, isZoomResize } from "@/lib/performance-utils";

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  imageLoadTime: number;
  zoomEvents: number;
  resizeEvents: number;
  averageZoomTime: number;
  averageResizeTime: number;
}

export function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const previousDimensionsRef = useRef({ width: 0, height: 0 });
  const tracker = PerformanceTracker.getInstance();

  useEffect(() => {
    // Initialize metrics
    const initialMetrics: PerformanceMetrics = {
      loadTime: 0,
      renderTime: 0,
      imageLoadTime: 0,
      zoomEvents: 0,
      resizeEvents: 0,
      averageZoomTime: 0,
      averageResizeTime: 0
    };

    // Monitor page load performance
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();

      entries.forEach((entry) => {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          setMetrics(prev => ({
            ...initialMetrics,
            ...prev,
            loadTime: navEntry.loadEventEnd - navEntry.startTime,
            renderTime: navEntry.domContentLoadedEventEnd - navEntry.startTime,
          }));
        }
      });
    });

    observer.observe({ entryTypes: ['navigation'] });

    // Monitor image loading
    const images = document.querySelectorAll('img');
    let imageLoadStart = performance.now();
    let imagesLoaded = 0;

    images.forEach((img) => {
      if (img.complete) {
        imagesLoaded++;
      } else {
        img.addEventListener('load', () => {
          imagesLoaded++;
          if (imagesLoaded === images.length) {
            setMetrics(prev => prev ? {
              ...prev,
              imageLoadTime: performance.now() - imageLoadStart
            } : null);
          }
        });
      }
    });

    // Monitor resize and zoom events
    const handleResize = () => {
      const currentDimensions = {
        width: window.innerWidth,
        height: window.innerHeight
      };

      if (previousDimensionsRef.current.width > 0) {
        const isZoom = isZoomResize(previousDimensionsRef.current, currentDimensions);

        tracker.startMeasure(isZoom ? 'zoom-event' : 'resize-event');

        // Simulate the end of the event after a short delay
        setTimeout(() => {
          const duration = tracker.endMeasure(isZoom ? 'zoom-event' : 'resize-event');

          setMetrics(prev => {
            if (!prev) return prev;

            if (isZoom) {
              return {
                ...prev,
                zoomEvents: prev.zoomEvents + 1,
                averageZoomTime: tracker.getAverageTime('zoom-event')
              };
            } else {
              return {
                ...prev,
                resizeEvents: prev.resizeEvents + 1,
                averageResizeTime: tracker.getAverageTime('resize-event')
              };
            }
          });
        }, 50);
      }

      previousDimensionsRef.current = currentDimensions;
    };

    // Set initial dimensions
    previousDimensionsRef.current = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    window.addEventListener('resize', handleResize);

    // Show metrics after 3 seconds
    const timer = setTimeout(() => setIsVisible(true), 3000);

    return () => {
      observer.disconnect();
      window.removeEventListener('resize', handleResize);
      clearTimeout(timer);
    };
  }, []);

  if (!metrics || !isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-3 rounded-lg text-xs font-mono z-50 max-w-xs">
      <div className="mb-1">📊 Performance Metrics</div>
      <div>Load: {Math.round(metrics.loadTime)}ms</div>
      <div>Render: {Math.round(metrics.renderTime)}ms</div>
      <div>Images: {Math.round(metrics.imageLoadTime)}ms</div>
      <div className="border-t border-white/20 mt-2 pt-2">
        <div className="mb-1">🔍 Zoom & Resize</div>
        <div>Zoom events: {metrics.zoomEvents}</div>
        <div>Resize events: {metrics.resizeEvents}</div>
        {metrics.averageZoomTime > 0 && (
          <div>Avg zoom: {Math.round(metrics.averageZoomTime)}ms</div>
        )}
        {metrics.averageResizeTime > 0 && (
          <div>Avg resize: {Math.round(metrics.averageResizeTime)}ms</div>
        )}
      </div>
      <button
        onClick={() => setIsVisible(false)}
        className="mt-2 text-xs bg-white/20 px-2 py-1 rounded"
      >
        Hide
      </button>
    </div>
  );
}
