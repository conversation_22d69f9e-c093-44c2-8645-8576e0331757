[build]
  command = "npm run build"

# Use Netlify's Next.js plugin for full Next.js support
[[plugins]]
  package = "@netlify/plugin-nextjs"

# Redirect www to non-www
[[redirects]]
  from = "https://www.upzera.com/*"
  to = "https://upzera.com/:splat"
  status = 301
  force = true

# Redirect root path to default locale
[[redirects]]
  from = "/"
  to = "/en"
  status = 302
  force = false

# Handle locale-specific redirects for better routing
[[redirects]]
  from = "/en/*"
  to = "/en/:splat"
  status = 200
  force = false

[[redirects]]
  from = "/lt/*"
  to = "/lt/:splat"
  status = 200
  force = false

# Catch-all for SPA routing - must be last
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false

# Headers for CORS and security
[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"

[[headers]]
  for = "/company_knowledge.json"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Cache-Control = "public, max-age=3600"

# Security headers with CSP to block tracking scripts
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "SAMEORIGIN"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://assets.calendly.com https://calendly.com https://*.calendly.com https://*.spline.design https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://assets.calendly.com https://*.calendly.com; font-src 'self' https://fonts.gstatic.com https://assets.calendly.com https://*.calendly.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.calendly.com https://calendly.com https://*.calendly.com https://*.spline.design https://prod.spline.design; frame-src 'self' https://calendly.com https://*.calendly.com; child-src 'self' https://calendly.com https://*.calendly.com; worker-src 'self' blob:; object-src 'none'; base-uri 'self'"



# Environment variables (these will be set in Netlify dashboard)
# MAILGUN_API_KEY
# MAILGUN_DOMAIN
# MAILGUN_FROM
# MAILGUN_REGION
# NEXT_PUBLIC_OPENAI_API_KEY
