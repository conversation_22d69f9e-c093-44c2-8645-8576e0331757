import { Link } from '@/i18n/navigation';
import NewsletterSubscribe from "./NewsletterSubscribe";
import { useTranslations } from 'next-intl';

export default function Footer() {
  const t = useTranslations('Footer');
  return (
    <footer className="bg-purple-950 text-white py-8 px-4 sm:py-12 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Mobile-optimized layout: 2 columns on mobile, 3 on desktop (was 4 with social networks) */}
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-12">
          {/* Column 1: References */}
          <div className="text-left">
            <h4 className="font-semibold mb-4 sm:mb-6 text-white text-base sm:text-lg">{t('references')}</h4>
            <ul className="space-y-2 sm:space-y-3 text-purple-100/80 text-sm sm:text-base">
              <li><Link href="/our-approach" className="hover:text-white transition-colors block">{t('approach')}</Link></li>
              <li><Link href="/about" className="hover:text-white transition-colors block">{t('aboutUs')}</Link></li>
              <li><Link href="/testimonials" className="hover:text-white transition-colors block">{t('testimonials')}</Link></li>
              <li><Link href="/faq" className="hover:text-white transition-colors block">{t('faq')}</Link></li>
              <li><Link href="/contact" className="hover:text-white transition-colors block">{t('contactUs')}</Link></li>
            </ul>
          </div>

          {/* Column 2: Services */}
          <div className="text-left">
            <h4 className="font-semibold mb-4 sm:mb-6 text-white text-base sm:text-lg">{t('services')}</h4>
            <ul className="space-y-2 sm:space-y-3 text-purple-100/80 text-sm sm:text-base">
              <li><Link href="/website-development" className="hover:text-white transition-colors block">{t('websiteDevelopment')}</Link></li>
              <li><Link href="/chatbot-integration" className="hover:text-white transition-colors block">{t('chatbotIntegration')}</Link></li>
            </ul>
          </div>

          {/* Column 3: Subscribe - Full width on mobile, positioned after services */}
          <div className="col-span-2 sm:col-span-1 text-left sm:text-left order-last sm:order-none">
            <h4 className="font-semibold mb-4 sm:mb-6 text-white text-base sm:text-lg">{t('newsletter')}</h4>
            <NewsletterSubscribe
              containerClassName="max-w-full sm:max-w-xs mx-0 mb-2"
              inputClassName="bg-purple-900/50 border-purple-700/50 text-white placeholder:text-purple-100/50 focus:border-purple-500 focus:ring-purple-500 text-sm sm:text-base"
              buttonClassName="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white text-sm sm:text-base"
              buttonIcon={true}
            />
          </div>
        </div>

        {/* Bottom section with logo and copyright - Mobile optimized */}
        <div className="border-t border-purple-800/30 pt-6 sm:pt-8 flex flex-col items-center space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
          <div>
             <Link href="/" className="flex items-center justify-center">
               <span className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">
                 UpZera
               </span>
             </Link>
          </div>
          <p className="text-purple-100/60 text-xs sm:text-sm text-center">
            {t('copyright')}
          </p>
        </div>
      </div>
    </footer>
  );
}
