import type { Metadata, Viewport } from 'next';
import { NextIntlClientProvider, hasLocale } from 'next-intl';
import { getMessages, getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';
import Navbar from '@/components/navbar';
import Footer from '@/components/footer';
import ChatbotEmbed from '@/components/ChatbotEmbed';
import LocaleProvider from '@/components/LocaleProvider';
import CookieConsent from '@/components/CookieConsent';

export async function generateViewport({
  params
}: {
  params: Promise<{locale: string}>;
}): Promise<Viewport> {
  return {
    width: 'device-width',
    initialScale: 1,
    themeColor: '#581c87',
  };
}

export async function generateMetadata({
  params
}: {
  params: Promise<{locale: string}>;
}): Promise<Metadata> {
  const {locale} = await params;
  const t = await getTranslations({locale, namespace: 'Metada<PERSON>'});

  const siteUrl = process.env.NODE_ENV === 'production' 
    ? 'https://upzera.com' 
    : 'http://localhost:3000';

  return {
    title: {
      default: t('title'),
      template: `%s | ${t('siteName')}`
    },
    description: t('description'),
    keywords: t('keywords'),
    authors: [{ name: 'UpZera Team' }],
    creator: 'UpZera',
    publisher: 'UpZera',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(siteUrl),
    alternates: {
      canonical: `${siteUrl}/${locale}`,
      languages: {
        'en': `${siteUrl}/en`,
        'lt': `${siteUrl}/lt`,
      },
    },
    openGraph: {
      title: t('title'),
      description: t('description'),
      url: `${siteUrl}/${locale}`,
      siteName: t('siteName'),
      images: [
        {
          url: `${siteUrl}/logo/upzera-og-image.png`,
          width: 1200,
          height: 630,
          alt: t('siteName'),
        },
      ],
      locale: locale,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
      images: [`${siteUrl}/logo/upzera-og-image.png`],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    icons: {
      icon: [
        { url: '/favicons/favicon.ico' },
        { url: '/favicons/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
        { url: '/favicons/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      ],
      apple: [
        { url: '/favicons/apple-touch-icon.png', sizes: '180x180' },
      ],
      other: [
        { url: '/favicons/android-chrome-192x192.png', sizes: '192x192', type: 'image/png' },
        { url: '/favicons/android-chrome-512x512.png', sizes: '512x512', type: 'image/png' },
      ],
    },
    manifest: '/favicons/site.webmanifest',
    other: {
      'theme-color': '#581c87',
      'msapplication-navbutton-color': '#581c87',
      'apple-mobile-web-app-status-bar-style': 'black-translucent',
    },
  };
}

export async function generateStaticParams() {
  return routing.locales.map((locale) => ({locale}));
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{locale: string}>;
}) {
  const {locale} = await params;

  // Ensure that the incoming `locale` is valid
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <LocaleProvider>
        <Navbar />
        <main>{children}</main>
        <Footer />
        <ChatbotEmbed />
        <CookieConsent />
      </LocaleProvider>
    </NextIntlClientProvider>
  );
}
