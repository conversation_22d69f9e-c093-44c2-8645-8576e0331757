import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NODE_ENV === 'production' 
    ? 'https://upzera.com' 
    : 'http://localhost:3000'

  const routes = [
    '',
    '/about',
    '/contact',
    '/faq',
    '/testimonials',
    '/our-approach',
    '/website-development',
    '/chatbot-integration'
  ]

  const locales = ['en', 'lt']

  const sitemap: MetadataRoute.Sitemap = []

  // Add root URL (redirects to /en) with highest priority
  sitemap.push({
    url: baseUrl,
    lastModified: new Date(),
    changeFrequency: 'daily',
    priority: 1.0,
    alternates: {
      languages: {
        en: `${baseUrl}/en`,
        lt: `${baseUrl}/lt`,
      }
    }
  })

  // Add localized homepages with highest priority
  locales.forEach(locale => {
    sitemap.push({
      url: `${baseUrl}/${locale}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1.0,
      alternates: {
        languages: {
          en: `${baseUrl}/en`,
          lt: `${baseUrl}/lt`,
        }
      }
    })
  })

  // Add other localized pages with lower priority
  locales.forEach(locale => {
    routes.slice(1).forEach(route => { // Skip empty route since we handled homepages above
      sitemap.push({
        url: `${baseUrl}/${locale}${route}`,
        lastModified: new Date(),
        changeFrequency: route === '/contact' ? 'weekly' : 'monthly',
        priority: route === '/contact' ? 0.8 : 0.7,
        alternates: {
          languages: {
            en: `${baseUrl}/en${route}`,
            lt: `${baseUrl}/lt${route}`,
          }
        }
      })
    })
  })

  return sitemap
}