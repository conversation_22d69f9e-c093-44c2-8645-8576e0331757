'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Container } from '@/components/ui/container';
import { Separator } from '@/components/ui/separator';

export default function PrivacyPolicyPage() {
  const t = useTranslations('PrivacyPolicy');

  return (
    <Container className="py-16 md:py-24">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl md:text-4xl font-bold mb-6">{t('title')}</h1>
        <p className="text-lg text-gray-600 mb-8">{t('lastUpdated')}: {t('lastUpdatedDate')}</p>
        
        <Separator className="my-8" />
        
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">{t('introduction.title')}</h2>
          <p className="mb-4">{t('introduction.p1')}</p>
          <p className="mb-4">{t('introduction.p2')}</p>
        </section>
        
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">{t('informationCollection.title')}</h2>
          <p className="mb-4">{t('informationCollection.p1')}</p>
          
          <h3 className="text-xl font-medium mt-6 mb-3">{t('informationCollection.personalData.title')}</h3>
          <ul className="list-disc pl-6 mb-6 space-y-2">
            <li>{t('informationCollection.personalData.item1')}</li>
            <li>{t('informationCollection.personalData.item2')}</li>
            <li>{t('informationCollection.personalData.item3')}</li>
            <li>{t('informationCollection.personalData.item4')}</li>
          </ul>
          
          <h3 className="text-xl font-medium mt-6 mb-3">{t('informationCollection.usageData.title')}</h3>
          <p className="mb-4">{t('informationCollection.usageData.p1')}</p>
        </section>
        
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">{t('cookies.title')}</h2>
          <p className="mb-4">{t('cookies.p1')}</p>
          <p className="mb-4">{t('cookies.p2')}</p>
          
          <h3 className="text-xl font-medium mt-6 mb-3">{t('cookies.types.title')}</h3>
          <ul className="list-disc pl-6 mb-6 space-y-4">
            <li>
              <strong>{t('cookies.types.essential.title')}</strong>: {t('cookies.types.essential.description')}
            </li>
            <li>
              <strong>{t('cookies.types.analytics.title')}</strong>: {t('cookies.types.analytics.description')}
            </li>
          </ul>
          
          <h3 className="text-xl font-medium mt-6 mb-3">{t('cookies.googleAnalytics.title')}</h3>
          <p className="mb-4">{t('cookies.googleAnalytics.p1')}</p>
          <p className="mb-4">{t('cookies.googleAnalytics.p2')}</p>
        </section>
        
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">{t('dataUse.title')}</h2>
          <p className="mb-4">{t('dataUse.p1')}</p>
          <ul className="list-disc pl-6 mb-6 space-y-2">
            <li>{t('dataUse.item1')}</li>
            <li>{t('dataUse.item2')}</li>
            <li>{t('dataUse.item3')}</li>
            <li>{t('dataUse.item4')}</li>
            <li>{t('dataUse.item5')}</li>
          </ul>
        </section>
        
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">{t('dataRetention.title')}</h2>
          <p className="mb-4">{t('dataRetention.p1')}</p>
        </section>
        
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">{t('dataProtection.title')}</h2>
          <p className="mb-4">{t('dataProtection.p1')}</p>
        </section>
        
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">{t('yourRights.title')}</h2>
          <p className="mb-4">{t('yourRights.p1')}</p>
          <ul className="list-disc pl-6 mb-6 space-y-2">
            <li>{t('yourRights.item1')}</li>
            <li>{t('yourRights.item2')}</li>
            <li>{t('yourRights.item3')}</li>
            <li>{t('yourRights.item4')}</li>
            <li>{t('yourRights.item5')}</li>
          </ul>
        </section>
        
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">{t('contact.title')}</h2>
          <p className="mb-4">{t('contact.p1')}</p>
          <p className="mb-4">
            <strong>Email:</strong> <EMAIL>
          </p>
        </section>
      </div>
    </Container>
  );
}
