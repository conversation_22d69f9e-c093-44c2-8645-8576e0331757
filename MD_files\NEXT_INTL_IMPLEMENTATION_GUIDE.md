# Next-intl Implementation Guide for UpZera

## Overview
This guide covers implementing internationalization (i18n) for UpZera website using next-intl to support English and Lithuanian languages.

## Tasks Breakdown

### 1. Installation & Dependencies
```bash
npm install next-intl
```

### 2. Project Structure Setup

#### Create Translation Files Structure
```
messages/
├── en.json      # English translations
└── lt.json      # Lithuanian translations
```

#### Update App Router Structure
```
app/
├── [locale]/
│   ├── layout.tsx
│   ├── page.tsx
│   ├── about/
│   │   └── page.tsx
│   ├── contact/
│   │   └── page.tsx
│   ├── testimonials/
│   │   └── page.tsx
│   ├── website-development/
│   │   └── page.tsx
│   ├── chatbot-integration/
│   │   └── page.tsx
│   ├── faq/
│   │   └── page.tsx
│   └── our-approach/
│       └── page.tsx
├── globals.css
├── layout.tsx   # Root layout
└── page.tsx     # Redirect to default locale
```

### 3. Configuration Files

#### Create `i18n.ts` Configuration
```typescript
import {notFound} from 'next/navigation';
import {getRequestConfig} from 'next-intl/server';

// Can be imported from a shared config
const locales = ['en', 'lt'];

export default getRequestConfig(async ({locale}) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) notFound();

  return {
    messages: (await import(`./messages/${locale}.json`)).default
  };
});
```

#### Update `next.config.js`
```javascript
const createNextIntlPlugin = require('next-intl/plugin');

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Your existing config
};

module.exports = withNextIntl(nextConfig);
```

#### Create `middleware.ts`
```typescript
import createMiddleware from 'next-intl/middleware';

export default createMiddleware({
  // A list of all locales that are supported
  locales: ['en', 'lt'],

  // Used when no locale matches
  defaultLocale: 'en'
});

export const config = {
  // Match only internationalized pathnames
  matcher: ['/', '/(lt|en)/:path*']
};
```

### 4. Root Layout Updates

#### Update Root `app/layout.tsx`
- Remove navbar, footer, and main content
- Keep only basic HTML structure
- Add locale detection and redirect logic

#### Create New `app/[locale]/layout.tsx`
- Move navbar, footer, and ChatbotEmbed here
- Add next-intl providers
- Handle locale-specific metadata

### 5. Page Migration Tasks

#### For Each Page Component:
1. **Move to `app/[locale]/` directory**
2. **Add locale parameter to page props**
3. **Import and use translation hooks**
4. **Extract all hardcoded text to translation keys**

#### Translation Hook Usage:
```typescript
import {useTranslations} from 'next-intl';

export default function HomePage() {
  const t = useTranslations('HomePage');
  
  return (
    <h1>{t('title')}</h1>
  );
}
```

### 6. Component Updates

#### Update All Components That Contain Text:
- `components/navbar.tsx` - Menu items, labels
- `components/footer.tsx` - Footer text, links
- `components/ui/contact-button.tsx` - Button text
- `components/chatbot/*` - All chatbot messages
- `components/ui/faq-section.tsx` - FAQ content
- All page-specific components

#### Pattern for Component Updates:
```typescript
// Before
<Button>Contact Us</Button>

// After
import {useTranslations} from 'next-intl';

const t = useTranslations('Common');
<Button>{t('contactUs')}</Button>
```

### 7. Translation Content Creation

#### Extract All Text Content From:
- **Navigation menus** (Home, About, Services, FAQ, Contact)
- **Page titles and headings**
- **Button labels and CTAs**
- **Form labels and placeholders**
- **Error messages**
- **Chatbot conversations**
- **FAQ questions and answers**
- **Footer content**
- **SEO metadata**

#### Create Translation Keys Structure:
```json
{
  "Common": {
    "contactUs": "Contact Us",
    "getStarted": "Get Started",
    "learnMore": "Learn More"
  },
  "Navigation": {
    "home": "Home",
    "about": "About",
    "services": "Services",
    "testimonials": "Testimonials",
    "faq": "FAQ",
    "contact": "Contact"
  },
  "HomePage": {
    "title": "We Don't Just Build Websites",
    "subtitle": "We create intelligent digital experiences"
  }
}
```

### 8. Language Switcher Component

#### Create `components/LanguageSwitcher.tsx`:
- Dropdown or button group for language selection
- Use `useRouter` and `usePathname` for navigation
- Preserve current page when switching languages
- Add flag icons or language codes

### 9. SEO & Metadata

#### Update Metadata for Each Locale:
- Page titles in each language
- Meta descriptions
- Open Graph tags
- Hreflang tags for SEO

#### Example Metadata Structure:
```typescript
export async function generateMetadata({params: {locale}}) {
  const t = await getTranslations({locale, namespace: 'Metadata'});
  
  return {
    title: t('title'),
    description: t('description'),
  };
}
```

### 10. API Routes & Forms

#### Update Form Handling:
- Contact forms
- Newsletter subscriptions
- Chatbot interactions
- Support tickets

#### Considerations:
- Email templates in multiple languages
- Error messages localization
- Success messages localization

### 11. URL Structure & Routing

#### Implement URL Patterns:
```
/ → /en (redirect)
/en/about → English about page
/lt/about → Lithuanian about page
```

#### Update All Internal Links:
```typescript
// Before
<Link href="/about">About</Link>

// After  
import {Link} from 'next-intl/navigation';
<Link href="/about">About</Link>
```

### 12. Dynamic Content Handling

#### Chatbot System:
- Translate all predefined responses
- Update intent detection for multiple languages
- Handle language-specific conversation flows

#### FAQ System:
- Translate all questions and answers
- Maintain FAQ structure across languages

### 13. Testing Tasks

#### Test Each Language:
- All pages render correctly
- Navigation works in all languages
- Forms submit properly
- Chatbot responds in correct language
- Language switcher functions
- SEO metadata is correct

#### Test Language Switching:
- Preserves current page
- Updates all text content
- Maintains user session/state

### 14. Content Translation

#### Translation Methods:
1. **Professional Lithuanian Translation Services** (recommended for business content)
2. **AI Translation + Human Review** (faster, cost-effective)
3. **Native Lithuanian Speaker Translation** (highest quality)

#### Priority Order:
1. Navigation and core UI elements
2. Homepage and key landing pages
3. Contact forms and CTAs
4. FAQ content
5. Detailed page content
6. Chatbot responses

### 15. Performance Optimization

#### Bundle Splitting:
- Load only required language files
- Implement lazy loading for translations
- Optimize image assets with locale-specific content

#### Caching Strategy:
- Cache translation files
- Configure proper CDN headers
- Implement service worker for offline support

### 16. Deployment Considerations

#### Environment Variables:
- Set default locale
- Configure locale detection strategy
- Update hosting configuration

#### Build Process:
- Verify all locales build correctly
- Test static generation for each language
- Validate translation file loading

## File Changes Summary

### New Files to Create:
- `i18n.ts`
- `middleware.ts`
- `messages/en.json`
- `messages/lt.json`
- `components/LanguageSwitcher.tsx`
- `app/[locale]/layout.tsx`
- `app/[locale]/page.tsx`

### Files to Modify:
- `next.config.js`
- `app/layout.tsx`
- All page components (move to locale folder)
- All components with text content
- `package.json` (add dependency)

### Files to Move:
- Move all pages from `app/` to `app/[locale]/`
- Preserve API routes in `app/api/`

## Resources

- [next-intl Documentation](https://next-intl.dev/)
- [Next.js Internationalization](https://nextjs.org/docs/app/building-your-application/routing/internationalization)
- [Lithuanian Translation Guidelines](https://www.vlkk.lt/)

## Estimated Timeline

- **Setup & Configuration**: 1-2 days
- **Content Extraction**: 1-2 days  
- **Component Updates**: 2-3 days
- **Lithuanian Translation Creation**: 2-3 days (depending on method)
- **Testing & Refinement**: 1-2 days
- **Total**: 7-12 days

## Success Criteria

- ✅ All pages accessible in both English and Lithuanian
- ✅ SEO-friendly URLs for each locale
- ✅ Language switcher works correctly
- ✅ Forms and chatbot work in both languages
- ✅ Professional Lithuanian translation quality
- ✅ No broken links or missing translations
- ✅ Performance remains optimal