"use client";

import React from 'react';
import {
  MessageSquare,
  Lightbulb,
  Rocket,
  <PERSON>tings,
  CheckCircle,
  HeartHandshake,
  FileText, // Added FileText icon
} from 'lucide-react';
import InteractiveApproachStep from './InteractiveApproachStep';
import { GradientBorder } from '@/components/ui/gradient-border';
import { useTranslations } from 'next-intl';

// Helper component for each step - with increased size
const ApproachStep = ({
  icon: Icon,
  title,
  description,
  bgColor,
  iconBgColor = 'bg-white/20', // Default icon background
  textColor = 'text-white',
  descriptionColor = 'text-purple-100/80',
  alignLeft = false, // Controls alignment for alternating layout
}: {
  icon: React.ElementType;
  title: string;
  description: string;
  bgColor: string;
  iconBgColor?: string;
  textColor?: string;
  descriptionColor?: string;
  alignLeft?: boolean;
}) => (
  <div className={`flex ${alignLeft ? 'md:justify-start' : 'md:justify-end'} justify-center w-full`}>
    <div
      className={`relative ${bgColor} ${textColor} md:p-10 p-6 md:rounded-full rounded-2xl shadow-lg w-full max-w-2xl md:flex md:items-start md:space-x-6 transition-all duration-300 hover:shadow-xl hover:translate-y-[-5px] cursor-pointer approach-block`}
    >
      <div
        className={`flex-shrink-0 w-16 h-16 ${iconBgColor} rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 md:mb-0 mb-4`}
      >
        <Icon className="w-8 h-8" />
      </div>
      <div className="md:ml-0 ml-2">
        <h3 className="text-2xl font-semibold mb-3 md:text-left text-center">{title}</h3>
        <p className={`text-lg ${descriptionColor} md:text-left text-center`}>{description}</p>
      </div>
    </div>
  </div>
);

// Updated Connector component with wiggly pulsing paths for mobile
const Connector = ({
  id,
  d,
  height = 200,
  width = 320,
  alignLeft = false,
  gradientColors = false
}: {
  id: string;
  d: string;
  height?: number;
  width?: number;
  alignLeft?: boolean;
  gradientColors?: boolean;
}) => {
  // Mobile wiggly line path
  const mobileWigglyPath = "M 50 20 Q 65 30, 50 50 Q 35 70, 50 90 Q 65 110, 50 130 Q 35 150, 50 190";

  return (
    <div className={`relative md:${alignLeft ? 'ml-[calc(50%+1.5rem)]' : 'mr-[calc(50%+1.5rem)]'} h-${height / 4} flex md:${alignLeft ? 'justify-start' : 'justify-end'} justify-center items-center my-[-1rem] md:my-[-1.5rem]`}>
      <svg
        width={width}
        height={height}
        viewBox={`0 0 ${width} ${height}`}
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={`absolute z-0 ${alignLeft ? 'md:left-[-160px]' : 'md:right-[-160px]'} mobile-connector`}
      >
        <defs>
          {gradientColors && (
            <linearGradient id={`gradient-${id}`} x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#EC4899" />
              <stop offset="100%" stopColor="#3B82F6" />
            </linearGradient>
          )}
        </defs>

        {/* Desktop path (visible on md and above) */}
        <path
          className="hidden md:block"
          d={d}
          stroke={gradientColors ? `url(#gradient-${id})` : "#EC4899"}
          strokeWidth="8"
          strokeLinecap="round"
        />

        {/* Mobile wiggly line (visible only on mobile) */}
        <path
          className="md:hidden block"
          d={mobileWigglyPath}
          stroke={gradientColors ? "#8B5CF6" : "#EC4899"}
          strokeWidth="4"
          strokeLinecap="round"
          strokeDasharray="1, 8"
          strokeDashoffset="0"
          style={{
            animation: "dash 15s linear infinite",
          }}
        />
      </svg>
    </div>
  );
};

const OurApproachPage = () => {
  const t = useTranslations('ApproachPage');
  const headingColor = 'text-white';
  const subHeadingColor = 'text-purple-200/90';

  // Adjusted S-curves for larger pill boxes
  const connectorPath1 = "M 310 10 C 310 80, 50 100, 10 190"; // Right to Left
  const connectorPath2 = "M 10 10 C 10 80, 270 100, 310 190"; // Left to Right

  return (
    <div className="min-h-screen overflow-x-hidden bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 py-16 md:py-24 px-4">
      <div className="container mx-auto max-w-4xl text-center mb-16 md:mb-20">
        <h1 className={`text-4xl md:text-5xl font-bold mb-4 ${headingColor} pt-6 md:pt-8`}>
          {t('title')}
        </h1>
        <p className={`text-lg md:text-xl ${subHeadingColor}`}>
          {t('subtitle')}
        </p>
      </div>

      {/* Steps Container with increased spacing */}
      <div className="relative max-w-4xl mx-auto flex flex-col items-center space-y-24">
        {/* Step 1 */}
        <InteractiveApproachStep stepName={t('step1Title')}>
          <ApproachStep
            icon={MessageSquare}
            title={t('step1Title')}
            description={t('step1Description')}
            bgColor="bg-purple-600"
            alignLeft={false} // Aligns right
          />
        </InteractiveApproachStep>

        {/* Connector 1 */}
        <Connector id="c1" d={connectorPath1} alignLeft={false} />

        {/* Step 2 */}
        <InteractiveApproachStep stepName={t('step2Title')}>
          <ApproachStep
            icon={Lightbulb}
            title={t('step2Title')}
            description={t('step2Description')}
            bgColor="bg-pink-600"
            alignLeft={true} // Aligns left
          />
        </InteractiveApproachStep>

        {/* Connector 2 */}
         <Connector id="c2" d={connectorPath2} alignLeft={true} />

        {/* Step 3 */}
        <InteractiveApproachStep stepName={t('step3Title')}>
          <ApproachStep
            icon={FileText} // Changed icon to better represent scope/contract
            title={t('step3Title')}
            description={t('step3Description')}
            bgColor="bg-purple-600"
            alignLeft={false} // Aligns right
          />
        </InteractiveApproachStep>

        {/* Connector 3 */}
        <Connector id="c3" d={connectorPath1} alignLeft={false} />

        {/* Step 4 */}
        <InteractiveApproachStep stepName={t('step4Title')}>
          <ApproachStep
            icon={Rocket} // Kept Rocket icon for Kickoff
            title={t('step4Title')}
            description={t('step4Description')}
            bgColor="bg-pink-600"
            alignLeft={true} // Aligns left
          />
        </InteractiveApproachStep>

        {/* Connector 4 */}
        <Connector id="c4" d={connectorPath2} alignLeft={true} />

        {/* Step 5 */}
        <InteractiveApproachStep stepName={t('step5Title')}>
          <ApproachStep
            icon={Settings} // Kept Settings icon for Build
            title={t('step5Title')}
            description={t('step5Description')}
            bgColor="bg-blue-600" // Blue color
            alignLeft={false} // Aligns right
          />
        </InteractiveApproachStep>

         {/* Connector 5 with gradient transition */}
         <Connector id="c5" d={connectorPath1} alignLeft={false} gradientColors={true} />

        {/* Step 6 */}
        <InteractiveApproachStep stepName={t('step6Title')}>
          <ApproachStep
            icon={CheckCircle} // Kept CheckCircle for Delivery
            title={t('step6Title')}
            description={t('step6Description')}
            bgColor="bg-cyan-600" // Cyan/Teal color
            alignLeft={true} // Aligns left
          />
        </InteractiveApproachStep>
      </div>

      {/* Gradient border at the top of footer */}

    </div>
  );
};

export default OurApproachPage;
