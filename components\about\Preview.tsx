"use client"; // Assuming this component needs client-side interactivity (matter-js)

import React from 'react';
import { Gravity, MatterBody } from '@/components/ui/gravity'; // Use alias path
import { useTranslations } from 'next-intl';

// It's good practice to define props if needed, even if empty for now
interface PreviewProps {}

const Preview: React.FC<PreviewProps> = () => {
  const t = useTranslations('AboutPage');

  // Define UpZera gradient colors for value blocks
  const valueColors = {
    Honesty: "bg-gradient-to-br from-blue-500 to-purple-600",
    Responsibility: "bg-gradient-to-br from-green-500 to-purple-600",
    ClientCare: "bg-gradient-to-br from-pink-500 to-purple-600",
    Trustworthiness: "bg-gradient-to-br from-purple-500 to-pink-500",
  };

  return (
    // Updated with UpZera styling - dark background with purple gradients
    <section className="max-w-4xl mx-auto min-h-[450px] flex flex-col relative font-azeretMono mb-16 md:mb-24 bg-purple-950/80 backdrop-blur-sm rounded-2xl py-10 px-4 overflow-hidden border border-purple-800/30 group">
      {/* Added gradient background overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-800/10 to-pink-800/10 rounded-2xl"></div>

      {/* Animated background elements for UpZera styling */}
      <div className="absolute w-32 h-32 bg-purple-600/10 rounded-full -top-10 -left-10 blur-2xl animate-pulse"></div>
      <div className="absolute w-32 h-32 bg-pink-600/10 rounded-full -bottom-10 -right-10 blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>

      {/* Updated text colors for dark theme with UpZera styling */}
      <h2 className="text-3xl md:text-4xl font-semibold mb-8 text-center text-white relative z-10">
        {t('valuesInteractiveTitle')}
      </h2>
      {/* Gravity container with UpZera styling */}
      <div className="relative w-full flex-grow min-h-[350px] z-10">
        <Gravity
          gravity={{ x: 0, y: 1 }}
          className="w-full h-full"
          resetOnResize={false}
          addTopWall={true} // Add top wall to prevent escape
          debug={false}
        >
          {/* Value Blocks with UpZera styling */}
          <MatterBody
            matterBodyOptions={{
              friction: 0.8, // Increased friction to prevent sliding
              restitution: 0.2, // Slightly more bounce for better physics
              density: 0.003, // Increased density for more stable physics
              frictionAir: 0.01 // Add air resistance to slow down movement
            }}
            x="40%"
            y="10%" // Moved down slightly from top
          >
            <div className={`text-lg sm:text-xl md:text-2xl ${valueColors.Honesty} text-white rounded-xl shadow-lg shadow-purple-500/30 hover:cursor-grab hover:shadow-xl hover:shadow-purple-500/40 hover:scale-105 transition-all duration-300 px-6 py-3 border border-purple-400/20 backdrop-blur-sm`}>
              {t('honestyTitle')}
            </div>
          </MatterBody>
          <MatterBody
            matterBodyOptions={{
              friction: 0.8,
              restitution: 0.2,
              density: 0.003,
              frictionAir: 0.01
            }}
            x="60%"
            y="15%" // Moved down slightly from top
          >
            <div className={`text-lg sm:text-xl md:text-2xl ${valueColors.Responsibility} text-white rounded-xl shadow-lg shadow-purple-500/30 hover:cursor-grab hover:shadow-xl hover:shadow-purple-500/40 hover:scale-105 transition-all duration-300 px-6 py-3 border border-purple-400/20 backdrop-blur-sm`}>
              {t('responsibilityTitle')}
            </div>
          </MatterBody>
          <MatterBody
            matterBodyOptions={{
              friction: 0.8,
              restitution: 0.2,
              density: 0.003,
              frictionAir: 0.01
            }}
            x="30%"
            y="20%" // Moved down slightly from top
          >
            <div className={`text-lg sm:text-xl md:text-2xl ${valueColors.ClientCare} text-white rounded-xl shadow-lg shadow-purple-500/30 hover:cursor-grab hover:shadow-xl hover:shadow-purple-500/40 hover:scale-105 transition-all duration-300 px-6 py-3 border border-purple-400/20 backdrop-blur-sm`}>
              {t('clientCareTitle')}
            </div>
          </MatterBody>
          <MatterBody
            matterBodyOptions={{
              friction: 0.8,
              restitution: 0.2,
              density: 0.003,
              frictionAir: 0.01
            }}
            x="70%"
            y="10%" // Moved down slightly from top
          >
            <div className={`text-lg sm:text-xl md:text-2xl ${valueColors.Trustworthiness} text-white rounded-xl shadow-lg shadow-purple-500/30 hover:cursor-grab hover:shadow-xl hover:shadow-purple-500/40 hover:scale-105 transition-all duration-300 px-6 py-3 border border-purple-400/20 backdrop-blur-sm`}>
              {t('trustworthinessTitle')}
            </div>
          </MatterBody>
        </Gravity>
      </div>
    </section>
  );
};

// Export using named export
export { Preview };
