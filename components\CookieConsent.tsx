'use client';

import React, { useEffect, useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import Cookies from 'js-cookie';
import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>, Shield, BarChart3 } from 'lucide-react';

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

const CookieConsent: React.FC = () => {
  const [showBanner, setShowBanner] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);
  const t = useTranslations('CookieConsent');
  const locale = useLocale();

  useEffect(() => {
    // Check if user has already made a choice
    const consent = Cookies.get('cookie-consent');
    if (!consent) {
      setShowBanner(true);
    } else if (consent === 'accepted') {
      // Load Google Analytics if previously accepted
      loadGoogleAnalytics();
    }
  }, []);

  const loadGoogleAnalytics = () => {
    // Create and append the gtag script
    const script1 = document.createElement('script');
    script1.async = true;
    script1.src = 'https://www.googletagmanager.com/gtag/js?id=G-HVDEH27ND2';
    document.head.appendChild(script1);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag(...args: any[]) {
      window.dataLayer.push(args);
    }
    window.gtag = gtag;
    
    gtag('js', new Date());
    gtag('config', 'G-HVDEH27ND2', {
      page_title: document.title,
      page_location: window.location.href,
    });

    console.log('✅ Google Analytics loaded with consent');
  };

  const handleAcceptAll = () => {
    Cookies.set('cookie-consent', 'accepted', { expires: 365 });
    setShowBanner(false);
    setShowPreferences(false);
    loadGoogleAnalytics();
    
    // Track consent acceptance
    if (window.gtag) {
      window.gtag('event', 'cookie_consent', {
        event_category: 'engagement',
        event_label: 'accepted_all'
      });
    }
  };

  const handleDeclineAll = () => {
    Cookies.set('cookie-consent', 'declined', { expires: 365 });
    setShowBanner(false);
    setShowPreferences(false);
    
    // Remove any existing GA cookies
    const gaCookies = document.cookie.split(';').filter(cookie => 
      cookie.trim().startsWith('_ga') || cookie.trim().startsWith('_gid')
    );
    
    gaCookies.forEach(cookie => {
      const cookieName = cookie.split('=')[0].trim();
      Cookies.remove(cookieName);
      Cookies.remove(cookieName, { domain: '.upzera.com' });
      Cookies.remove(cookieName, { domain: 'upzera.com' });
    });

    console.log('❌ Analytics cookies declined and removed');
  };

  const handleManagePreferences = () => {
    setShowPreferences(true);
  };

  const handleSavePreferences = (analyticsEnabled: boolean) => {
    if (analyticsEnabled) {
      handleAcceptAll();
    } else {
      handleDeclineAll();
    }
  };

  if (!showBanner) return null;

  return (
    <>
      {/* Cookie Consent Banner */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg">
        <div className="max-w-7xl mx-auto p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-start gap-3 flex-1">
              <Cookie className="h-6 w-6 text-purple-600 mt-1 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-1">
                  {t('title')}
                </h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  {t('description')}{' '}
              
                </p>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              <Button
                variant="outline"
                size="sm"
                onClick={handleManagePreferences}
                className="text-gray-600 border-gray-300 hover:bg-gray-50"
              >
                {t('managePreferences')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDeclineAll}
                className="text-gray-600 border-gray-300 hover:bg-gray-50"
              >
                {t('declineAll')}
              </Button>
              <Button
                size="sm"
                onClick={handleAcceptAll}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
              >
                {t('acceptAll')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Cookie Preferences Modal */}
      {showPreferences && (
        <CookiePreferencesModal
          onClose={() => setShowPreferences(false)}
          onSave={handleSavePreferences}
        />
      )}
    </>
  );
};

interface CookiePreferencesModalProps {
  onClose: () => void;
  onSave: (analyticsEnabled: boolean) => void;
}

const CookiePreferencesModal: React.FC<CookiePreferencesModalProps> = ({
  onClose,
  onSave,
}) => {
  const [analyticsEnabled, setAnalyticsEnabled] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const t = useTranslations('CookieConsent');

  useEffect(() => {
    // Trigger animation after component mounts
    setIsVisible(true);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    // Wait for animation to complete before calling onClose
    setTimeout(onClose, 200);
  };

  const handleSave = () => {
    setIsVisible(false);
    // Wait for animation to complete before calling onSave
    setTimeout(() => onSave(analyticsEnabled), 200);
  };

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center transition-all duration-200 ${
        isVisible ? 'bg-black bg-opacity-50' : 'bg-black bg-opacity-0'
      }`}
    >
      <div
        className={`bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto transition-all duration-200 transform ${
          isVisible ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-4 scale-95'
        }`}
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              {t('preferencesTitle')}
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="space-y-6">
            {/* Essential Cookies */}
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-green-600" />
                  <h3 className="font-medium text-gray-900">
                    {t('essentialCookies')}
                  </h3>
                </div>
                <span className="text-sm text-green-600 font-medium">
                  {t('alwaysActive')}
                </span>
              </div>
              <p className="text-sm text-gray-600">
                {t('essentialDescription')}
              </p>
            </div>

            {/* Analytics Cookies */}
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                  <h3 className="font-medium text-gray-900">
                    {t('analyticsCookies')}
                  </h3>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={analyticsEnabled}
                    onChange={(e) => setAnalyticsEnabled(e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                </label>
              </div>
              <p className="text-sm text-gray-600">
                {t('analyticsDescription')}
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={handleClose}
              className="text-gray-600 border-gray-300 hover:bg-gray-50"
            >
              {t('cancel')}
            </Button>
            <Button
              onClick={handleSave}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
            >
              {t('savePreferences')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
