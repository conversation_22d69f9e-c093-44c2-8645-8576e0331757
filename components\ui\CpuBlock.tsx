import { CpuArchitecture } from "@/components/ui/cpu-architecture";
import { Rocket } from "lucide-react"; // Import Rocket icon

interface CpuBlockProps {
  floatingKeyword1?: string;
  floatingKeyword2?: string;
  floatingKeyword3?: string;
  floatingKeyword4?: string;
}

export function CpuBlock({
  floatingKeyword1 = "CONVERSION-FOCUSED",
  floatingKeyword2 = "ZERO EFFORT",
  floatingKeyword3 = "AGENTIC-AI",
  floatingKeyword4 = "START TODAY!"
}: CpuBlockProps) {
  return (
    <div className="relative">
      <div className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-lg blur-3xl"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-cyan-500/10 rounded-lg blur-xl transform scale-90"></div>

      <div className="relative bg-gradient-to-br from-purple-900/60 to-purple-800/40 rounded-xl border border-purple-700/50 backdrop-blur-sm p-10 transition-all duration-500 hover:scale-[1.02] hover:shadow-lg hover:shadow-purple-500/20 group">
        <div className="w-full h-[450px] md:h-[500px] relative overflow-hidden">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-48 h-48 rounded-full bg-gradient-to-r from-pink-500/10 to-purple-500/10 animate-pulse"></div>
          </div>

          <CpuArchitecture
            className="text-white/90 group-hover:text-white/100 transition-colors duration-300"
            text=" "
            width="100%"
            height="100%"
            lineMarkerSize={24}
            showCpuConnections={true}
            animateText={true}
            animateLines={true}
            animateMarkers={true}
          />
          {/*Rocket icon */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <Rocket className="w-6 h-6 md:w-8 md:h-8 text-white/80 group-hover:text-white/100 transition-colors duration-300 mr-2" />
          </div>

          <div className="absolute top-[15%] right-[20%] text-[10px] text-cyan-400/70 cpu-float-label">{floatingKeyword1}</div>
          <div className="absolute bottom-[25%] left-[15%] text-[10px] text-pink-400/70 cpu-float-label-alt">{floatingKeyword3}</div>
          <div className="absolute bottom-[15%] right-[30%] text-[10px] text-purple-300/70 cpu-float-label">{floatingKeyword4}</div>
          <div className="absolute top-[25%] left-[25%] text-[10px] text-green-400/70 cpu-float-label-alt">{floatingKeyword2}</div>
        </div>
      </div>
    </div>
  );
}