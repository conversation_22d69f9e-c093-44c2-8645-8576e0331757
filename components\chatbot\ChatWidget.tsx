'use client';

import { useState, useEffect, useRef } from 'react';
import { useViewportChange } from '@/lib/performance-utils';
import { useTranslations, useLocale } from 'next-intl';
import { saveLead } from '../../lib/supabase';
import ChatHeader from './ChatHeader';
import MessageList from './MessageList';
import LeadForm from './LeadForm';
import ChatInput from './ChatInput';
import { IntentDetector } from './intent-detector';
import { ConversationStates, ConversationState, TIMEOUT_DURATION, WARNING_DURATION, SCREEN_BREAKPOINTS } from './constants';
import { EnhancedAIResponder, createEnhancedResponder, AIResponseContext } from '../../lib/enhanced-ai-responder';
import {
  Message,
  ScreenSize,
  UserContext,
  LeadFormData,
  CompanyKnowledge,
  SupportTicketData
} from './types';

export default function ChatWidget() {
  const t = useTranslations('Chatbot');
  const locale = useLocale();

  const [isOpen, setIsOpen] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [isExpanding, setIsExpanding] = useState(false);
  const [showChatContent, setShowChatContent] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [companyKnowledge, setCompanyKnowledge] = useState<CompanyKnowledge>({});
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [conversationState, setConversationState] = useState<ConversationState>(ConversationStates.WELCOME);
  const [userContext, setUserContext] = useState<UserContext>({
    name: '',
    email: '',
    lastIntent: null,
    selectedService: null,
    awaitingResponse: false
  });
  const [enhancedResponder, setEnhancedResponder] = useState<EnhancedAIResponder | null>(null);
  const [leadForm, setLeadForm] = useState<LeadFormData>({
    name: '',
    email: '',
    isSubmitting: false,
    error: null
  });
  const [showLeadForm, setShowLeadForm] = useState(false);
  const [showCalendly, setShowCalendly] = useState(false);
  const [isConversationEnded, setIsConversationEnded] = useState(false);
  const [lastActivityTime, setLastActivityTime] = useState(Date.now());
  const [timeoutWarningShown, setTimeoutWarningShown] = useState(false);
  const [screenSize, setScreenSize] = useState<ScreenSize>({
    width: 1200,
    height: 800,
    isSmall: false,
  });
  const [isClient, setIsClient] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        chatContainerRef.current!.scrollTop = chatContainerRef.current!.scrollHeight;
      }, 100);
    }
  };

  // Handle Kraken-style opening animation
  const handleOpen = () => {
    setIsOpen(true);
    setIsExpanding(true);

    // Start the expansion animation
    setTimeout(() => {
      // Show chat content after the widget has expanded
      setShowChatContent(true);
      setIsExpanding(false);
    }, 400); // Match the animation duration
  };

  // Handle smooth closing animation
  const handleClose = () => {
    setIsClosing(true);
    setShowChatContent(false);

    // Wait for animation to complete before actually closing
    setTimeout(() => {
      setIsOpen(false);
      setIsClosing(false);
    }, 400); // Match the animation duration
  };

  // Conversation Flow Management
  const updateActivityTime = () => {
    setLastActivityTime(Date.now());
    setTimeoutWarningShown(false);
  };

  const transitionToState = (newState: ConversationState, context: Partial<UserContext> = {}) => {
    setConversationState(newState);
    setUserContext(prev => ({ ...prev, ...context }));
    updateActivityTime();
  };

  // Helper function to end conversation with smooth transition
  const endConversationWithTransition = (delay: number = 1500) => {
    setTimeout(() => {
      setIsConversationEnded(true);
    }, delay);
  };

  // Enhanced function to ensure proper message formatting
  const formatMessageForDisplay = (text: string): string => {
    if (!text || typeof text !== 'string') return text;

    // Apply consistent formatting rules to all bot messages
    let formattedText = text;

    // Add line breaks after sentences that end with periods, exclamation marks, or question marks
    // but only if they're followed by a capital letter or "For example" (indicating a new sentence/section)
    formattedText = formattedText.replace(/([.!?])\s+([A-Z]|For example)/g, '$1\n\n$2');

    // Add line breaks before "For example," patterns
    formattedText = formattedText.replace(/\s+(For example,)/gi, '\n\n$1');

    // Add line breaks after price ranges and before new information
    formattedText = formattedText.replace(/(€\d+[,\d]*(?:\s*to\s*€\d+[,\d]*)?[.,]?)\s+([A-Z])/g, '$1\n\n$2');

    // Add line breaks before "After" patterns (like "After a free consultation")
    formattedText = formattedText.replace(/\s+(After\s+)/gi, '\n\n$1');

    // Add spacing around key pricing phrases that should stand out
    formattedText = formattedText.replace(/(landing pages start from|full websites range from|chatbot setups typically cost|chatbots typically cost)/gi, '\n\n$1');

    // Add line breaks before concluding statements
    formattedText = formattedText.replace(/\s+(After a free consultation,|You'll receive)/gi, '\n\n$1');

    return formattedText;
  };

  const addBotMessage = (text: string, type: string | null = null, delay = 800) => {
    // Debug: Log all bot messages being added
    console.log('📤 Adding bot message:', text.substring(0, 100) + (text.length > 100 ? '...' : ''));
    if (text.includes('palaikymo užklausą') || text.includes('support ticket') || text.includes('Pušku') || text.includes('Sukurtau')) {
      console.log('🎫 SUPPORT TICKET MESSAGE DETECTED:', text);
      console.log('🎫 Current conversation state:', conversationState);
      console.log('🎫 Stack trace:', new Error().stack);
    }

    setTimeout(() => {
      setMessages(prev => {
        // Format the message for better display
        const formattedText = formatMessageForDisplay(text);

        // Prevent duplicate messages (especially for buttons/types)
        const lastMessage = prev[prev.length - 1];
        if (lastMessage && !lastMessage.isUser && lastMessage.text === formattedText && lastMessage.type === (type || undefined)) {
          console.log('🚫 Prevented duplicate message:', formattedText, type);
          return prev; // Don't add duplicate
        }
        return [...prev, { text: formattedText, isUser: false, type: type || undefined }];
      });
      setIsBotTyping(false);
    }, delay);
  };

  const addBotMessages = (messageArray: (string | { type: string })[], delay = 800) => {
    setTimeout(() => {
      setMessages(prev => [
        ...prev,
        ...messageArray.map(msg =>
          typeof msg === 'string'
            ? { text: formatMessageForDisplay(msg), isUser: false }
            : { ...msg, text: '', isUser: false }
        )
      ]);
      setIsBotTyping(false);

      // Check if calendly widget was added and schedule follow-up message
      const hasCalendlyWidget = messageArray.some(msg =>
        typeof msg === 'object' && msg.type === 'calendly'
      );
      if (hasCalendlyWidget) {
        scheduleCalendlyFollowUp();
      }
    }, delay);
  };

  const scheduleCalendlyFollowUp = () => {
    // Wait 2 seconds after calendly is shown, then ask if user needs anything else
    setTimeout(() => {
      addBotMessage(t('letUsKnowAnythingElse'));
      transitionToState(ConversationStates.ANYTHING_ELSE);
    }, 2000);
  };

  // Set up CSS variables based on screen size
  useEffect(() => {
    const root = document.documentElement;
    if (screenSize.isSmall) {
      // Mobile-friendly sizes - different variations based on actual width
      if (screenSize.width <= SCREEN_BREAKPOINTS.EXTRA_SMALL) { // iPhone SE, smaller devices
        root.style.setProperty('--chat-font-size', '13px');
        root.style.setProperty('--chat-padding', '6px');
      } else if (screenSize.width <= SCREEN_BREAKPOINTS.SMALL) { // iPhone X/11/12 mini
        root.style.setProperty('--chat-font-size', '14px');
        root.style.setProperty('--chat-padding', '8px');
      } else { // Other mobile devices
        root.style.setProperty('--chat-font-size', '15px');
        root.style.setProperty('--chat-padding', '10px');
      }
    } else {
      // Reset to default sizes
      root.style.setProperty('--chat-font-size', '16px');
      root.style.setProperty('--chat-padding', '16px');
    }
  }, [screenSize]);

  // Initialize welcome flow when chat opens
  useEffect(() => {
    if (isOpen && conversationState === ConversationStates.WELCOME && messages.length === 0) {
      setIsBotTyping(true);
      
      // First message - Welcome message with initial delay
      setTimeout(() => {
        setMessages([
          { text: t('welcomeMessage'), isUser: false }
        ]);
        setIsBotTyping(true); // Keep typing indicator for the next message
        
        // Second message - Description with natural pause
        setTimeout(() => {
          setMessages(prev => [
            ...prev,
            { text: t('welcomeDescription'), isUser: false }
          ]);
          setIsBotTyping(true); // Keep typing indicator for menu
          
          // Third element - Main menu with final pause
          setTimeout(() => {
            setMessages(prev => [
              ...prev,
              { type: 'mainMenu', text: '', isUser: false }
            ]);
            setIsBotTyping(false);
            transitionToState(ConversationStates.MAIN_MENU);
          }, 1500); // Pause before showing menu options
        }, 1800); // Natural pause between messages
      }, 1000); // Initial delay before first message
    }
  }, [isOpen, conversationState, messages.length, t]);

  // Timeout and Re-engagement Handling
  useEffect(() => {
    if (!isOpen || isConversationEnded) return;

    const checkInactivity = () => {
      const now = Date.now();
      const timeSinceLastActivity = now - lastActivityTime;

      // Show warning at 4 minutes
      if (timeSinceLastActivity >= WARNING_DURATION && !timeoutWarningShown) {
        setTimeoutWarningShown(true);
        setIsBotTyping(true);
        addBotMessage(t('areYouStillThere'));
      }

      // End conversation at 5 minutes
      if (timeSinceLastActivity >= TIMEOUT_DURATION) {
        setIsBotTyping(true);
        addBotMessage(t('beenAwayForAWhile'));
        transitionToState(ConversationStates.CONVERSATION_ENDED);
        endConversationWithTransition(2000); // 2 second delay for smooth transition
      }
    };

    const interval = setInterval(checkInactivity, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [isOpen, isConversationEnded, lastActivityTime, timeoutWarningShown]);

  // Initialize client-side state and screen size with zoom awareness
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Use optimized viewport change detection
  useViewportChange(({ width, height, isZoom }) => {
    const isSmall = width <= SCREEN_BREAKPOINTS.MEDIUM;

    // Only update screen size if it's not a zoom operation or if the change is significant
    if (!isZoom || Math.abs(width - (screenSize?.width || 0)) > 100) {
      setScreenSize({ width, height, isSmall });
    }
  }, 150); // 150ms debounce

  useEffect(() => {
    scrollToBottom();
  }, [messages, showLeadForm]);

  // Initialize enhanced AI responder and knowledge base
  useEffect(() => {
    const initializeEnhancedSystem = async () => {
      try {
        // Load traditional knowledge base
        const response = await fetch('/company_knowledge.json');
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();
        setCompanyKnowledge(data);

        // Initialize enhanced AI responder
        const apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY;
        if (apiKey) {
          const responder = createEnhancedResponder(apiKey);
          setEnhancedResponder(responder);

          // Initialize knowledge base in background with current locale
          responder.initializeKnowledgeBase(locale).catch(err => {
            console.warn('Enhanced knowledge base initialization failed, using fallback:', err);
          });
        }
      } catch (err) {
        console.error('Error loading knowledge base:', err);
        // Don't show alert to user, just log the error
      }
    };

    initializeEnhancedSystem();
  }, []);

  const findKnowledgeMatch = (question: string) => {
    // Add null/undefined check to prevent TypeError
    if (!question || typeof question !== 'string') {
      console.log('⚠️ findKnowledgeMatch received invalid question:', question);
      return null;
    }

    const lower = question.toLowerCase();
    console.log('🔍 findKnowledgeMatch searching for:', question);

    // Don't use knowledge base for human contact requests
    if (/speak|talk|connect|chat|contact|representative|person|human|agent|real/i.test(lower)) return null;

    // PRIORITY MATCHING: Check for pricing queries first with exact patterns
    if (/\b(price|prices|pricing|cost|costs|how much|charge|charges|budget|rate|rates|fee|fees)\b/i.test(lower)) {
      console.log('🎯 PRIORITY: Detected pricing query, searching for pricing FAQ');
      for (const faq of companyKnowledge.faqs || []) {
        if (faq.keywords && Array.isArray(faq.keywords) && faq.keywords.some(k => k && ['cost', 'price', 'pricing', 'budget', 'billing'].includes(k.toLowerCase()))) {
          console.log('✅ Found pricing FAQ match:', faq.answer);
          return faq.answer;
        }
      }
    }

    // Enhanced keyword matching for FAQs with better scoring
    let bestMatch = null;
    let bestScore = 0;

    for (const faq of companyKnowledge.faqs || []) {
      let score = 0;
      // Add safety check for keywords array
      if (!faq.keywords || !Array.isArray(faq.keywords)) continue;

      for (const keyword of faq.keywords) {
        // Add safety check for each keyword
        if (!keyword || typeof keyword !== 'string') continue;

        const keywordLower = keyword.toLowerCase();
        if (lower.includes(keywordLower)) {
          // Give higher score for exact matches and longer keywords
          score += keyword.length > 3 ? 2 : 1;
        }
        // Also check for partial matches at word boundaries
        const words = lower.split(/\s+/);
        for (const word of words) {
          if (word === keywordLower) {
            score += 3; // Exact word match gets highest score
          } else if (word.includes(keywordLower) || keywordLower.includes(word)) {
            score += 1; // Partial word match gets some score
          }
        }
      }
      if (score > bestScore) {
        bestScore = score;
        bestMatch = faq.answer;
      }
    }

    // If we found a good FAQ match, return it (lowered threshold)
    if (bestScore >= 1) {
      console.log('✅ findKnowledgeMatch found FAQ match (score:', bestScore, '):', bestMatch);
      return bestMatch;
    }

    // Enhanced service matching with detailed responses
    for (const service of companyKnowledge.services || []) {
      let score = 0;
      for (const keyword of service.keywords) {
        if (lower.includes(keyword.toLowerCase())) {
          score += keyword.length > 3 ? 2 : 1;
        }
      }
      if (score >= 2) {
        // Return more detailed service info
        const priceInfo = service.service_tiers?.[0]?.price_range ?
          ` Starting from ${service.service_tiers[0].price_range}.` : '';
        const response = `${service.name}: ${service.description}${priceInfo}`;
        console.log('✅ findKnowledgeMatch found service match (score:', score, '):', response);
        return response;
      }
    }

    // Company info queries - more flexible founding date patterns
    if (/when.*(upzera|company).*(founded|established|created|started)|founding.*(year|date)|(upzera|company).*(founded|established|created|started)/i.test(lower)) {
      return `UpZera was founded in ${companyKnowledge.company_info?.founding_year || 2025}. We're based in ${companyKnowledge.company_info?.location || 'Eindhoven, Netherlands'}.`;
    }

    if (/where|location|address|based/i.test(lower)) {
      return `We're located at ${companyKnowledge.company_info?.address || 'Eindhoven, Netherlands'}. Feel free to reach out anytime!`;
    }

    if (/mission|purpose|goal/i.test(lower)) {
      return companyKnowledge.company_info?.mission || 'We build smart digital tools that actually move businesses forward.';
    }

    // Greetings with context
    if (/hello|hi|hey|good morning|good afternoon|good evening/i.test(lower)) {
      const greetings = companyKnowledge.conversation_flows?.greetings || [t('fallbackGreeting')];
      const response = greetings[Math.floor(Math.random() * greetings.length)];
      console.log('✅ findKnowledgeMatch found greeting:', response);
      return response;
    }

    console.log('❌ findKnowledgeMatch found no match');
    return null;
  };

  const getAIResponse = async (userInput: string) => {
    try {
      // Try enhanced responder first if available
      if (enhancedResponder) {
        const conversationHistory = messages.slice(-6).map(m =>
          m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
        );

        const context: AIResponseContext = {
          userQuery: userInput,
          conversationHistory,
          locale,
          userContext: {
            name: userContext.name,
            email: userContext.email,
            previousTopics: [userContext.lastIntent].filter(Boolean) as string[]
          }
        };

        const enhancedResponse = await enhancedResponder.generateResponse(context);

        // If confidence is high enough, use enhanced response
        if (enhancedResponse.confidence > 0.3) {
          return enhancedResponse.content;
        }
      }

      // Fallback to original AI response system
      return await getOriginalAIResponse(userInput);

    } catch (error) {
      console.error('Enhanced AI Error:', error);
      return await getOriginalAIResponse(userInput);
    }
  };

  const getOriginalAIResponse = async (userInput: string) => {
    try {
      // Get more conversation context (last 6 messages instead of 3) but optimize for tokens
      const recentMessages = messages.slice(-6).map(m =>
        m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
      ).join('\n');

      // Create comprehensive but token-optimized knowledge context
      const knowledgeContext = {
        company: {
          name: companyKnowledge.company_info?.name,
          mission: companyKnowledge.company_info?.mission,
          location: companyKnowledge.company_info?.location,
          tagline: companyKnowledge.company_info?.tagline,
          values: companyKnowledge.company_info?.values?.slice(0, 4), // Limit to key values
        },
        services: companyKnowledge.services?.map(s => ({
          name: s.name,
          description: s.description,
          price: s.service_tiers?.[0]?.price_range || 'Contact for pricing'
        })) || [],
        contact: {
          email: companyKnowledge.contact_info?.email,
          phone: companyKnowledge.contact_info?.phone,
          calendly: companyKnowledge.contact_info?.calendly_url
        },
        faqs: companyKnowledge.faqs?.slice(0, 8).map(f => ({ // Limit to top 8 FAQs
          q: f.question,
          a: f.answer
        })) || []
      };

      const languageInstruction = locale === 'lt'
        ? `IMPORTANT: Respond in Lithuanian language (lietuvių kalba). Use natural, professional Lithuanian throughout your response.

LITHUANIAN TERMINOLOGY GUIDELINES:
- Use "nukreipimo puslapis/puslapiai" instead of "nusileidimo puslapiai" for landing pages
- Use "svetainių kūrimas" for web development
- Use "potencialių klientų pritraukimas" for lead generation
- Use "konsultacija" for consultation
- Use "paslaugos" for services`
        : 'IMPORTANT: Respond in English language.';

      const prompt = `You are UpZera's AI assistant. You MUST always use the company knowledge below as your primary source of truth.

${languageInstruction}

COMPANY KNOWLEDGE (ALWAYS USE THIS):
${JSON.stringify(knowledgeContext, null, 2)}

CONVERSATION CONTEXT:
${recentMessages}

USER QUESTION: "${userInput}"

INSTRUCTIONS:
- ALWAYS check the knowledge base first for any information about UpZera
- If the user asks about founding, history, location, services, pricing - use the exact information from the knowledge base
- Keep responses SHORT (1-2 sentences max), FRIENDLY, and HELPFUL
- If asked about services, mention specific pricing when available
- Guide users toward booking a consultation when appropriate
- Be conversational but professional
- If you don't know something specific from the knowledge base, suggest contacting the team
- NEVER say you don't have information if it exists in the knowledge base above
- NEVER generate support ticket creation messages or confirmations - those are handled by the system
- DO NOT mention ticket numbers, support ticket creation, or use phrases like "Sukūriau" or "palaikymo užklausą"
- ${languageInstruction}

FORMATTING REQUIREMENTS:
- Structure your response with proper spacing for readability
- When mentioning multiple services or prices, separate them naturally
- Use sentence breaks to create digestible chunks of information
- Example format: "Our pricing is project-based and tailored to your specific needs. For example, landing pages start from €350, full websites range from €1,400 to €2,400, and chatbot setups typically cost between €500 and €1,200. After a free consultation, you'll receive a personalized quote!"

Respond now:`;

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini', // More cost-effective model
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
          max_tokens: 300 // Increased for complete responses, especially in Lithuanian
        })
      });

      const data = await response.json();

      if (data.error) {
        console.error('OpenAI API Error:', data.error);
        return t('aiErrorMessage');
      }

      return data.choices[0]?.message?.content?.trim() || t('aiErrorFallback');

    } catch (error) {
      console.error('AI Error:', error);
      return t('aiErrorFallback');
    }
  };

  const validateEmail = (email: string) => /^[^\s@]+@[^\s@]+$/.test(email);

  // Support ticket creation function
  const createSupportTicket = async (ticketData: SupportTicketData) => {
    try {
      // Format conversation history with smart truncation
      const filteredMessages = messages.filter(m => m.text && m.text.trim() !== '');

      let conversationHistory = '';
      const maxMessages = 8; // Show last 8 messages for context
      const messagesToShow = filteredMessages.slice(-maxMessages);

      // Add truncation notice if we're showing fewer messages than total
      if (filteredMessages.length > maxMessages) {
        const hiddenCount = filteredMessages.length - maxMessages;
        conversationHistory += `[... ${hiddenCount} earlier message${hiddenCount > 1 ? 's' : ''} truncated for brevity ...]\n\n`;
      }

      conversationHistory += messagesToShow
        .map((m) => {
          const speaker = m.isUser ? 'User' : 'Bot';
          const timestamp = new Date().toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
          });

          // Truncate very long messages
          let messageText = m.text.trim();
          if (messageText.length > 200) {
            messageText = messageText.substring(0, 200) + '... [message truncated]';
          }

          return `[${timestamp}] ${speaker}: ${messageText}`;
        })
        .join('\n\n');

      const response = await fetch('/api/support-ticket', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...ticketData,
          conversationHistory,
          locale
        }),
      });

      if (!response.ok) {
        let errorMessage = 'Failed to create support ticket';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch {
          // If response is not JSON, use default message
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      return result;
    } catch (error: any) {
      console.error('Error creating support ticket:', error);
      throw error;
    }
  };

  const handleLeadSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateEmail(leadForm.email)) {
      setLeadForm(prev => ({ ...prev, error: t('leadFormErrorEmail') }));
      return;
    }
    if (!leadForm.name.trim()) {
      setLeadForm(prev => ({ ...prev, error: t('leadFormErrorName') }));
      return;
    }
    setLeadForm(prev => ({ ...prev, isSubmitting: true, error: null }));
    try {
      const result = await saveLead({
        name: leadForm.name,
        email: leadForm.email,
        created_at: new Date().toISOString()
      });
      if (result.error) throw new Error(typeof result.error === 'string' ? result.error : 'Failed to save your information');
      setMessages(prev => [...prev, { text: t('leadFormSuccess', { name: leadForm.name }), isUser: false }]);
      setShowLeadForm(false);
      setLeadForm({ name: '', email: '', isSubmitting: false, error: null });
    } catch (error: any) {
      setLeadForm(prev => ({ ...prev, isSubmitting: false, error: error.message || t('leadFormErrorSubmit') }));
    }
  };

  // Handle QuickAction clicks - now with conversation state management
  const handleQuickAction = async (actionMessage: string, intent: string | null = null, skipAddingUserMessage: boolean = false) => {
    if (!skipAddingUserMessage) {
      setMessages(prev => [...prev, { text: actionMessage, isUser: true }]);
    }
    setIsBotTyping(true);
    updateActivityTime();

    // Handle based on intent or detect intent from message
    const detectedIntent = intent || IntentDetector.detectIntent(actionMessage)?.intent;

    switch (detectedIntent) {
      case 'prices':
        transitionToState(ConversationStates.PRICES);
        handlePricesFlow(actionMessage);
        return;

      case 'booking':
        transitionToState(ConversationStates.BOOKING);
        handleBookingFlow();
        return;

      case 'services':
        transitionToState(ConversationStates.SERVICES);
        handleServicesFlow();
        return;

      case 'web_development':
        handleSpecificServiceFlow(actionMessage);
        return;

      case 'chatbot_service':
        handleSpecificServiceFlow(actionMessage);
        return;

      case 'support_service':
        handleSpecificServiceFlow(actionMessage);
        return;

      case 'human_help':
      case 'detailed_support':
        console.log('Human help case triggered');
        transitionToState(ConversationStates.SUPPORT_TICKET_NAME);
        handleSupportTicketFlow();
        return;

      default:
        // Fallback to existing logic for other intents
        const intentMatch = IntentDetector.detectIntent(actionMessage);
        if (intentMatch) {
          const lastBotMessage = messages.length > 0 ?
            messages.filter(m => !m.isUser).slice(-1)[0]?.text || '' : '';

          const conversationContext = {
            lastBotMessage,
            messageCount: messages.length,
            hasShownCalendly: messages.some(m => m.type === 'calendly')
          };

          const intentResponse = IntentDetector.getIntentResponse(intentMatch, conversationContext);

          if (intentResponse) {
            const { messages: responseMessages, endConversation } = intentResponse;
            addBotMessages(responseMessages, 600);
            if (endConversation) {
              endConversationWithTransition();
            }
            return;
          }
        }

        // Fallback for quick actions - prioritize AI for Lithuanian users
        setTimeout(async () => {
          if (locale === 'lt') {
            const aiResponse = await getAIResponse(actionMessage);
            setMessages(prev => [...prev, { text: aiResponse, isUser: false }]);
          } else {
            // For English users, try knowledge base first (faster response)
            const localAnswer = findKnowledgeMatch(actionMessage);
            if (localAnswer) {
              setMessages(prev => [
                ...prev,
                { text: localAnswer, isUser: false },
                { text: t('wouldYouLikeToKnowMoreServices'), isUser: false }
              ]);
            } else {
              const aiResponse = await getAIResponse(actionMessage);
              setMessages(prev => [...prev, { text: aiResponse, isUser: false }]);
            }
          }
          setIsBotTyping(false);
        }, 800);
        break;
    }
  };

  // Flow Handlers
  const handlePricesFlow = async (userMessage: string) => {
    console.log('🔍 PRICING FLOW - Checking response source for:', userMessage);

    // Try enhanced responder first if available
    if (enhancedResponder) {
      console.log('🤖 Trying Enhanced AI Responder first...');
      try {
        const conversationHistory = messages.slice(-6).map(m =>
          m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
        );

        const context: AIResponseContext = {
          userQuery: userMessage,
          conversationHistory,
          locale,
          userContext: {
            name: userContext.name,
            email: userContext.email,
            previousTopics: [userContext.lastIntent].filter(Boolean) as string[]
          }
        };

        const enhancedResponse = await enhancedResponder.generateResponse(context);
        console.log('📊 Enhanced AI Response confidence:', enhancedResponse.confidence);

        // If confidence is high enough, use enhanced response
        if (enhancedResponse.confidence > 0.3) {
          console.log('✅ Using Enhanced AI Response (knowledge chunks)');
          addBotMessages([
            enhancedResponse.content,
            t('wouldYouLikeToKnowMorePricing')
          ]);
          transitionToState(ConversationStates.ANYTHING_ELSE);
          return;
        }
      } catch (error) {
        console.error('❌ Enhanced AI Responder failed:', error);
      }
    }

    // For Lithuanian users, prioritize AI response over knowledge base to ensure proper language
    if (locale === 'lt') {
      console.log('🔄 Using AI Response for Lithuanian user');
      const aiResponse = await getAIResponse(userMessage);
      addBotMessages([
        aiResponse,
        t('wouldYouLikeToKnowMorePricing')
      ]);
      transitionToState(ConversationStates.ANYTHING_ELSE);
      return;
    }

    // For English users, try knowledge base first (faster response)
    console.log('📚 Trying static knowledge base...');
    const localAnswer = findKnowledgeMatch(userMessage);
    if (localAnswer) {
      console.log('✅ Using Static Knowledge Base response');
      addBotMessages([
        localAnswer,
        t('wouldYouLikeToKnowMorePricing')
      ]);
      transitionToState(ConversationStates.ANYTHING_ELSE);
    } else {
      // Final ChatGPT Fallback
      console.log('🔄 Using Basic AI Response fallback');
      const aiResponse = await getAIResponse(userMessage);
      addBotMessage(aiResponse);
      transitionToState(ConversationStates.ANYTHING_ELSE);
    }
  };

  const handleBookingFlow = () => {
    addBotMessages([
      t('bookingMessage'),
      { type: 'calendly' }
    ]);
    // Note: Follow-up message is handled automatically by addBotMessages
  };

  // Handle responses in booking flow with contextual intent detection
  const handleBookingResponse = async (userMessage: string) => {
    // FIRST: Check for new specific intents (prices, services, etc.) - user might ask new questions
    const specificIntent = IntentDetector.detectIntent(userMessage);
    if (specificIntent && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service'].includes(specificIntent.intent)) {
      console.log('✅ Found specific intent in booking flow, routing to appropriate flow');
      handleQuickAction(userMessage, specificIntent.intent, true);
      return;
    }

    // SECOND: Check for yes/no responses to booking flow
    if (specificIntent?.intent === 'yes') {
      addBotMessage(t('greatToBookMeeting'));
      addBotMessage("", 'mainMenu', 1500);
      transitionToState(ConversationStates.MAIN_MENU);
      return;
    }

    if (specificIntent?.intent === 'no') {
      addBotMessage(t('noProblemAnythingElse'));
      addBotMessage("", 'mainMenu', 1200);
      transitionToState(ConversationStates.MAIN_MENU);
      return;
    }

    // If no specific intent found and not yes/no, just return to main menu
    console.log('🔄 No specific intent found in booking flow, returning to main menu');
    addBotMessage(t('gotIt'));
    addBotMessage("", 'mainMenu', 1000);
    transitionToState(ConversationStates.MAIN_MENU);
  };

  const handleServicesFlow = () => {
    addBotMessages([
      t('servicesMessage'),
      { type: 'serviceOptions' }
    ]);
    transitionToState(ConversationStates.SERVICE_SELECTION);
  };

  const handleSupportTicketFlow = () => {
    console.log('handleSupportTicketFlow called');
    setTimeout(() => {
      addBotMessage(t('supportTicketStart'));
      transitionToState(ConversationStates.SUPPORT_TICKET_NAME);
      console.log('State transitioned to SUPPORT_TICKET_NAME');
    }, 100);
  };

  const handleSpecificServiceFlow = async (userMessage: string) => {
    try {
      // Try enhanced responder first if available
      if (enhancedResponder) {
        const conversationHistory = messages.slice(-6).map(m =>
          m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
        );

        const context: AIResponseContext = {
          userQuery: userMessage,
          conversationHistory,
          locale,
          userContext: {
            name: userContext.name,
            email: userContext.email,
            previousTopics: [userContext.lastIntent].filter(Boolean) as string[]
          }
        };

        const enhancedResponse = await enhancedResponder.generateResponse(context);

        // If confidence is high enough, use enhanced response
        if (enhancedResponse.confidence > 0.3) {
          addBotMessage(enhancedResponse.content);
          // Show main menu after service info - no unpredictable follow-up questions
          addBotMessage("", 'mainMenu', 2000);
          transitionToState(ConversationStates.MAIN_MENU);
          return;
        }
      }

      // For Lithuanian users, prioritize AI response over knowledge base to ensure proper language
      if (locale === 'lt') {
        const aiResponse = await getAIResponse(userMessage);
        addBotMessage(aiResponse);
        // Show main menu after service info - no unpredictable follow-up questions
        addBotMessage("", 'mainMenu', 2000);
        transitionToState(ConversationStates.MAIN_MENU);
        return;
      }

      // For English users, try knowledge base first (faster response)
      const localAnswer = findKnowledgeMatch(userMessage);
      if (localAnswer) {
        addBotMessage(localAnswer);
        // Show main menu after service info - no unpredictable follow-up questions
        addBotMessage("", 'mainMenu', 2000);
        transitionToState(ConversationStates.MAIN_MENU);
      } else {
        // Final fallback to basic AI response
        const aiResponse = await getAIResponse(userMessage);
        addBotMessage(aiResponse);
        // Show main menu after service info - no unpredictable follow-up questions
        addBotMessage("", 'mainMenu', 2000);
        transitionToState(ConversationStates.MAIN_MENU);
      }
    } catch (error) {
      console.error('Error in handleSpecificServiceFlow:', error);
      // Error fallback - prioritize AI for Lithuanian users
      if (locale === 'lt') {
        try {
          const aiResponse = await getAIResponse(userMessage);
          addBotMessage(aiResponse);
        } catch (aiError) {
          addBotMessage(t('happyToHelpLearnServices'));
        }
        addBotMessage("", 'mainMenu', 1500);
        transitionToState(ConversationStates.MAIN_MENU);
      } else {
        const localAnswer = findKnowledgeMatch(userMessage);
        if (localAnswer) {
          addBotMessage(localAnswer);
          addBotMessage("", 'mainMenu', 1500);
          transitionToState(ConversationStates.MAIN_MENU);
        } else {
          addBotMessage(t('happyToHelpLearnServices'));
          addBotMessage("", 'mainMenu', 1500);
          transitionToState(ConversationStates.MAIN_MENU);
        }
      }
    }
  };

  // Enhanced contextual response handler with better intent analysis
  const handleContextualResponse = async (userMessage: string) => {
    console.log('handleContextualResponse called with:', userMessage, 'Current state:', conversationState);

    try {
      // First, try to understand if this is actually a yes/no response with more context
      const enhancedYesNoClassification = await classifyYesNoWithEnhancedContext(userMessage);

      if (enhancedYesNoClassification === 'yes') {
        console.log('✅ Enhanced classification detected YES - ending conversation');
        addBotMessage(t('pleasureToChat'));
        transitionToState(ConversationStates.CONVERSATION_ENDED);
        setIsConversationEnded(true);
        return;
      } else if (enhancedYesNoClassification === 'no') {
        console.log('✅ Enhanced classification detected NO - continuing conversation');
        addBotMessage(t('perfectHereToHelp'));
        addBotMessage("", 'mainMenu', 1200);
        transitionToState(ConversationStates.MAIN_MENU);
        return;
      }

      // If still unclear, provide contextual response
      let dynamicResponse = null;

      if (enhancedResponder) {
        console.log('🤖 Trying Enhanced AI for contextual response...');
        const conversationHistory = messages.slice(-5).map(m =>
          m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
        );

        const context: AIResponseContext = {
          userQuery: userMessage,
          conversationHistory,
          locale,
          userContext: {
            name: userContext.name,
            email: userContext.email,
            previousTopics: [userContext.lastIntent].filter(Boolean) as string[]
          }
        };

        const enhancedResponse = await enhancedResponder.generateResponse(context);

        // Use enhanced response if confidence is reasonable
        if (enhancedResponse.confidence > 0.15) {
          console.log('✅ Using Enhanced AI for contextual response');
          dynamicResponse = enhancedResponse.content;
        }
      }

      // Fallback to basic AI response if enhanced didn't work
      if (!dynamicResponse) {
        console.log('🔄 Using Basic AI for contextual response');
        dynamicResponse = await getAIResponse(userMessage);
      }

      // Add the dynamic response and continue conversation flow
      addBotMessage(dynamicResponse);
      setTimeout(() => {
        addBotMessage(t('anythingElseToday'));
        transitionToState(ConversationStates.END_CONVERSATION_CONFIRM);
      }, 1500);

    } catch (error) {
      console.error('Error in contextual response:', error);
      addBotMessage(t('makeSureUnderstandCorrectly'));
    }
  };

  // Random responses for unknown intent to make it more dynamic
  const getRandomUnknownResponse = (): string => {
    const responses = [
      t('happyToHelpMainWays'),
      t('letMeHelpFindServices'),
      t('hereToHelpMainAreas'),
      t('greatQuestionMainWays'),
      t('loveToAssistKeyAreas'),
      t('noProblemMainServices'),
      t('readyToHelpPrimaryWays'),
      t('letMeGuideMainOptions')
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  };

  // AI-powered intent classification function
  const classifyIntentWithAI = async (userMessage: string): Promise<string | null> => {
    try {
      const prompt = `You are an intent classifier for a web development and chatbot development company. You understand both English and Lithuanian messages.

Available intents/states:
- "booking" - for scheduling meetings, consultations, appointments, calls (Lithuanian: susitikimas, rezervacija, konsultacija)
- "prices" - for pricing information, costs, rates, how much things cost (Lithuanian: kainos, kainų, kiek kainuoja)
- "services" - for information about what services we offer (Lithuanian: paslaugos, ką darote, ko galite)
- "web_development" - for full-stack web development services specifically (Lithuanian: svetainių kūrimas, web development)
- "chatbot_service" - for chatbot development services specifically (Lithuanian: chatbot, pokalbių robotas)
- "human_help" - for talking to a real person, creating support tickets, getting human assistance (Lithuanian: žmogus, pagalba, palaikymas, support)
- "end_conversation" - for ending the chat, saying goodbye, finishing the conversation (Lithuanian: ačiū, viso gero, baigti, pabaiga)

User message: "${userMessage}"

Analyze this message and return ONLY the intent name that best matches, or "unknown" if none match clearly.
You have to analyze the message yourself, do not use any external knowledge.
Do not explain, just return the intent name.`;

      const response = await fetch('/api/ai-response', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: prompt })
      });

      if (!response.ok) throw new Error('AI classification failed');

      const data = await response.json();
      const classification = data.response?.trim().toLowerCase();

      console.log('🎯 AI classified intent as:', classification);

      // Return the classification if it's one of our valid intents
      if (['booking', 'prices', 'services', 'web_development', 'chatbot_service', 'support_service', 'human_help', 'end_conversation'].includes(classification)) {
        return classification;
      }

      return null;
    } catch (error) {
      console.error('❌ AI intent classification error:', error);
      return null;
    }
  };

  // Specialized AI classification for yes/no responses in confirmation contexts
  const classifyYesNoWithAI = async (userMessage: string, context: string): Promise<'yes' | 'no' | null> => {
    try {
      // Get recent conversation history for better context
      const recentMessages = messages.slice(-4).map(m =>
        `${m.isUser ? 'User' : 'Bot'}: ${m.text}`
      ).join('\n');

      const prompt = `You are analyzing a user's response to determine if they mean YES or NO. You understand both English and Lithuanian.

CONVERSATION CONTEXT:
${recentMessages}

CURRENT QUESTION: "${context}"
USER'S RESPONSE: "${userMessage}"

IMPORTANT CONTEXT NOTES:
- This is an end-of-conversation confirmation
- The question asks if the user is SURE they DON'T need help (double negative)
- Focus on SEMANTIC MEANING, not just yes/no keywords
- If user confirms "I don't need help" = END conversation
- If user indicates "I want help" = CONTINUE conversation

SEMANTIC ANALYSIS FOR END CONVERSATION:
END CONVERSATION (user confirms they don't need help):
- "nereikia daugiau pagalbos" = don't need more help = END
- "man nereikia" = I don't need (help) = END
- "išitikines jog nereikia" = sure I don't need = END
- "tikrai nereikia" = really don't need = END
- "I don't need more help" = END
- "I'm good/fine/all set" = END
- "taip, nereikia" = yes, don't need = END

CONTINUE CONVERSATION (user wants help):
- "reikia pagalbos" = need help = CONTINUE
- "turiu klausimų" = have questions = CONTINUE
- "noriu sužinoti" = want to know = CONTINUE
- "galbūt reikia" = maybe need = CONTINUE
- "I have questions" = CONTINUE
- "tell me more" = CONTINUE
- "wait" / "palauk" = CONTINUE

ANALYSIS INSTRUCTIONS:
1. IGNORE simple yes/no words - focus on whether user needs help or not
2. Look for phrases about needing/not needing help
3. "nereikia" in context of help = user doesn't need help = END
4. Questions or requests for info = user needs help = CONTINUE

Return ONLY: "yes", "no", or "unknown"`;

      const response = await fetch('/api/ai-response', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: prompt })
      });

      if (!response.ok) throw new Error('AI yes/no classification failed');

      const data = await response.json();
      const classification = data.response?.trim().toLowerCase();

      console.log('🎯 AI classified yes/no as:', classification);

      if (classification === 'yes' || classification === 'no') {
        return classification as 'yes' | 'no';
      }

      return null;
    } catch (error) {
      console.error('❌ AI yes/no classification error:', error);
      return null;
    }
  };

  // Enhanced yes/no classification with deeper conversation context
  const classifyYesNoWithEnhancedContext = async (userMessage: string): Promise<'yes' | 'no' | null> => {
    try {
      // Get more conversation history for better context
      const conversationHistory = messages.slice(-6).map(m =>
        `${m.isUser ? 'User' : 'Bot'}: ${m.text}`
      ).join('\n');

      const prompt = `You are analyzing a user's response in a conversation ending context. You understand both English and Lithuanian.

FULL CONVERSATION CONTEXT:
${conversationHistory}

USER'S CURRENT RESPONSE: "${userMessage}"

CONTEXT ANALYSIS:
- This is an end-of-conversation flow
- The bot is asking if the user is sure they don't need more help
- Look at the conversation flow to understand what the user was discussing
- Consider the user's overall intent and satisfaction level

CLASSIFICATION RULES:
1. IGNORE simple yes/no keywords - focus on SEMANTIC MEANING
2. If user confirms they DON'T need help (even using negative words): return "yes"
3. If user indicates they DO want help or have questions: return "no"
4. Look for phrases that confirm "no more help needed" vs "I want help"

SEMANTIC ANALYSIS PATTERNS:
YES (ready to end - user confirms they don't need help):
- "nereikia daugiau pagalbos" = don't need more help = END
- "man nereikia" = I don't need = END
- "išitikines jog nereikia" = sure I don't need = END
- "tikrai nereikia" = really don't need = END
- "gerai, nereikia" = okay, don't need = END
- "I don't need more help" = END
- "I'm good" = END
- "All set" = END

NO (wants to continue - user wants help):
- "reikia pagalbos" = need help = CONTINUE
- "turiu klausimų" = have questions = CONTINUE
- "noriu sužinoti" = want to know = CONTINUE
- "galbūt" = maybe = CONTINUE
- "I have questions" = CONTINUE
- "tell me more" = CONTINUE

Return ONLY: "yes", "no", or "unknown"`;

      const response = await fetch('/api/ai-response', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: prompt })
      });

      if (!response.ok) throw new Error('Enhanced AI classification failed');

      const data = await response.json();
      const classification = data.response?.trim().toLowerCase();

      console.log('🎯 Enhanced AI classified as:', classification);

      if (classification === 'yes' || classification === 'no') {
        return classification as 'yes' | 'no';
      }

      return null;
    } catch (error) {
      console.error('❌ Enhanced AI classification error:', error);
      return null;
    }
  };

  const handleOutOfContextInput = async (userMessage: string) => {
    console.log('handleOutOfContextInput called with:', userMessage, 'Current state:', conversationState);

    // FIRST: Check for specific intents and route directly to appropriate flows
    const intentMatch = IntentDetector.detectIntent(userMessage);
    console.log('🎯 Intent check in out-of-context:', intentMatch?.intent);

    if (intentMatch && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service'].includes(intentMatch.intent)) {
      console.log('✅ Found specific intent, routing to appropriate flow');
      handleQuickAction(userMessage, intentMatch.intent, true);
      return;
    }

    // ONLY if no specific intent is found, use AI to classify intent and route to appropriate state
    try {
      console.log('🤖 Using AI to classify intent and determine state...');

      // Use AI to determine which state/flow the user wants to go to
      const aiIntentClassification = await classifyIntentWithAI(userMessage);

      if (aiIntentClassification && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service', 'end_conversation'].includes(aiIntentClassification)) {
        console.log('✅ AI classified intent as:', aiIntentClassification);

        // Handle end conversation intent specially
        if (aiIntentClassification === 'end_conversation') {
          console.log('🔚 AI detected end conversation intent');
          addBotMessage(t('understandEndConversation'));
          transitionToState(ConversationStates.END_CONVERSATION_CONFIRM);
          return;
        }

        handleQuickAction(userMessage, aiIntentClassification, true);
        return;
      }

      // If AI couldn't classify to a specific intent, fall back to main menu
      console.log('🔄 AI could not classify intent, showing main menu');
      addBotMessage(getRandomUnknownResponse());
      addBotMessage("", 'mainMenu', 1200);
      transitionToState(ConversationStates.MAIN_MENU);

    } catch (error) {
      console.error('❌ AI intent classification failed, using fallback:', error);
      // Fallback to main menu if AI fails
      addBotMessage(t('happyToHelpShowAssist'));
      addBotMessage("", 'mainMenu', 1200);
      transitionToState(ConversationStates.MAIN_MENU);
    }
  };

  const handleAnythingElseFlow = async (userMessage: string) => {
    console.log('🔄 handleAnythingElseFlow called with:', userMessage);
    const intentMatch = IntentDetector.detectIntent(userMessage);
    console.log('🎯 Intent detected:', intentMatch?.intent);

    if (intentMatch?.intent === 'yes') {
      console.log('✅ User said YES - showing main menu');
      addBotMessage(t('greatWhatElseKnow'));
      addBotMessage("", 'mainMenu', 1200);
      transitionToState(ConversationStates.MAIN_MENU);
    } else if (intentMatch?.intent === 'no') {
      console.log('❌ User said NO - asking for confirmation');
      // Two-step confirmation before ending conversation
      addBotMessage(t('understandBeforeEndChat'));
      transitionToState(ConversationStates.END_CONVERSATION_CONFIRM);
    } else {
      console.log('❓ No clear yes/no intent detected - checking for specific requests');
      // Parse for specific request
      const specificIntent = IntentDetector.detectIntent(userMessage);
      if (specificIntent && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service'].includes(specificIntent.intent)) {
        handleQuickAction(userMessage, specificIntent.intent, true); // Skip adding user message since it's already added
      } else {
        // Out of context - use dedicated handler
        await handleOutOfContextInput(userMessage);
      }
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;
    setMessages(prev => [...prev, { text: inputValue, isUser: true }]);
    const userMessage = inputValue;
    setInputValue('');
    setIsBotTyping(true);
    updateActivityTime();

    console.log('handleSendMessage called with:', userMessage, 'Current state:', conversationState);
    console.log('🔄 Current user context:', userContext);

    // Handle based on current conversation state
    switch (conversationState) {
      case ConversationStates.MAIN_MENU:
        // Parse intent and route to appropriate flow
        const intentMatch = IntentDetector.detectIntent(userMessage);
        if (intentMatch && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service', 'end_conversation'].includes(intentMatch.intent)) {
          // Handle end conversation intent specially
          if (intentMatch.intent === 'end_conversation') {
            console.log('🔚 Detected end conversation intent from main menu');
            addBotMessage(t('understandEndConversation'));
            transitionToState(ConversationStates.END_CONVERSATION_CONFIRM);
            return;
          }

          handleQuickAction(userMessage, intentMatch.intent, true); // Skip adding user message since it's already added
        } else {
          // Out of context input - use dedicated handler
          await handleOutOfContextInput(userMessage);
        }
        break;

      case ConversationStates.BOOKING:
        await handleBookingResponse(userMessage);
        break;

      case ConversationStates.ANYTHING_ELSE:
        await handleAnythingElseFlow(userMessage);
        break;

      case ConversationStates.COLLECT_NAME:
        setUserContext(prev => ({ ...prev, name: userMessage.trim() }));
        addBotMessage(t('niceToMeetYouEmailPrompt', { name: userMessage.trim() }));
        transitionToState(ConversationStates.COLLECT_EMAIL);
        break;

      case ConversationStates.COLLECT_EMAIL:
        if (validateEmail(userMessage.trim())) {
          setUserContext(prev => ({ ...prev, email: userMessage.trim() }));
          addBotMessage(`${t('perfectConfirmDetails')}\n\n• **${t('nameLabel')}** ${userContext.name}\n• **${t('emailLabel')}** ${userMessage.trim()}\n\n${t('isInformationCorrect')}`);
          transitionToState(ConversationStates.CONFIRM_DETAILS);
        } else {
          addBotMessage(t('invalidEmailAddress'));
        }
        break;

      case ConversationStates.CONFIRM_DETAILS:
        const confirmIntent = IntentDetector.detectIntent(userMessage);
        if (confirmIntent?.intent === 'yes') {
          addBotMessage(t('greatConnectingTeam'));
          transitionToState(ConversationStates.CONVERSATION_ENDED);
          setIsConversationEnded(true);
        } else if (confirmIntent?.intent === 'no') {
          addBotMessage(t('noProblemReenterDetails'));
          transitionToState(ConversationStates.REENTER_DETAILS_CONFIRM);
        } else {
          addBotMessage(t('pleaseAnswerYesNoCorrect'));
        }
        break;

      case ConversationStates.REENTER_DETAILS_CONFIRM:
        const reenterIntent = IntentDetector.detectIntent(userMessage);
        if (reenterIntent?.intent === 'yes') {
          addBotMessage(t('sureStartOverName'));
          // Clear previous data
          setUserContext(prev => ({
            ...prev,
            name: '',
            email: ''
          }));
          transitionToState(ConversationStates.COLLECT_NAME);
        } else if (reenterIntent?.intent === 'no') {
          addBotMessage(t('noWorriesAnythingElse'));
          addBotMessage("", 'mainMenu', 1200);
          transitionToState(ConversationStates.MAIN_MENU);
        } else {
          addBotMessage(t('pleaseAnswerYesNoReenter'));
        }
        break;

      case ConversationStates.SUPPORT_TICKET_NAME:
        setUserContext(prev => ({ ...prev, name: userMessage.trim() }));
        addBotMessage(t('thankYouEmailPrompt', { name: userMessage.trim() }));
        transitionToState(ConversationStates.SUPPORT_TICKET_EMAIL);
        break;

      case ConversationStates.SUPPORT_TICKET_EMAIL:
        if (validateEmail(userMessage.trim())) {
          setUserContext(prev => ({ ...prev, email: userMessage.trim() }));
          addBotMessage(`${t('perfectProvideDescription')}\n\n${t('tipMoreDetails')}`);
          transitionToState(ConversationStates.SUPPORT_TICKET_DESCRIPTION);
        } else {
          addBotMessage(t('invalidEmailAddress'));
        }
        break;

      case ConversationStates.SUPPORT_TICKET_DESCRIPTION:
        setUserContext(prev => ({ ...prev, supportTicketDescription: userMessage.trim() }));
        addBotMessage(`${t('thankYouDetailedDescription')}

${t('confirmSupportTicketDetails')}

• **${t('nameLabel')}** ${userContext.name}
• **${t('emailLabel')}** ${userContext.email}
• **${t('problemLabel')}** ${userMessage.trim()}

${t('shouldCreateSupportTicket')}`);
        transitionToState(ConversationStates.SUPPORT_TICKET_CONFIRM);
        break;

      case ConversationStates.SUPPORT_TICKET_CONFIRM:
        console.log('🎫 In SUPPORT_TICKET_CONFIRM state, user message:', userMessage);
        const ticketConfirmIntent = IntentDetector.detectIntent(userMessage);
        console.log('🎫 Detected intent:', ticketConfirmIntent);
        if (ticketConfirmIntent?.intent === 'yes') {
          setIsBotTyping(true);
          try {
            const result = await createSupportTicket({
              name: userContext.name,
              email: userContext.email,
              problemDescription: userContext.supportTicketDescription || '',
              conversationHistory: ''
            });
            // Debug: Log the translation values
            console.log('🎫 Support ticket created, using translations:');
            console.log('ticketNumber:', result.ticketNumber);
            console.log('locale:', locale);

            // Use next-intl proper variable interpolation instead of manual replace
            const successMessage = t('supportTicketCreatedSuccess', { ticketNumber: result.ticketNumber });
            console.log('Final success message:', successMessage);

            const fullMessage = `${successMessage}

${t('nextStepsTitle')}
${t('teamWillReview')}
${t('responseWithin24Hours')}
${t('checkEmailForUpdates')}

${t('thankYouForContacting')}`;

            console.log('Full message to be sent:', fullMessage);
            addBotMessage(fullMessage);
            addBotMessage(t('anythingElseToday'));
            transitionToState(ConversationStates.ANYTHING_ELSE);
          } catch (error: any) {
            addBotMessage(t('apologizeIssueCreatingTicket'));
            transitionToState(ConversationStates.MAIN_MENU);
          }
        } else if (ticketConfirmIntent?.intent === 'no') {
          addBotMessage(t('noProblemStartOverTicket'));
          transitionToState(ConversationStates.SUPPORT_TICKET_RESTART_CONFIRM);
        } else {
          addBotMessage(t('pleaseAnswerYesNoCreateTicket'));
        }
        break;

      case ConversationStates.SUPPORT_TICKET_RESTART_CONFIRM:
        const restartIntent = IntentDetector.detectIntent(userMessage);
        if (restartIntent?.intent === 'yes') {
          addBotMessage(t('greatStartOverName'));
          // Clear previous ticket data
          setUserContext(prev => ({
            ...prev,
            name: '',
            email: '',
            supportTicketDescription: ''
          }));
          transitionToState(ConversationStates.SUPPORT_TICKET_NAME);
        } else if (restartIntent?.intent === 'no') {
          addBotMessage(t('noProblemAnythingElseToday'));
          addBotMessage("", 'mainMenu', 1200);
          transitionToState(ConversationStates.MAIN_MENU);
        } else {
          addBotMessage(t('pleaseAnswerYesNoStartOver'));
        }
        break;

      case ConversationStates.END_CONVERSATION_CONFIRM:
        console.log('🔚 END_CONVERSATION_CONFIRM state - user message:', userMessage);
        const endConfirmIntent = IntentDetector.detectIntent(userMessage);
        console.log('🎯 End confirm intent detected:', endConfirmIntent?.intent);

        if (endConfirmIntent?.intent === 'yes') {
          // User said "yes" to "are you sure you don't need assistance" = end conversation
          console.log('✅ User confirmed ending conversation (yes = sure they dont need help)');
          addBotMessage(t('pleasureToChat'));
          transitionToState(ConversationStates.CONVERSATION_ENDED);
          setIsConversationEnded(true);
        } else if (endConfirmIntent?.intent === 'no') {
          // User said "no" to "are you sure you don't need assistance" = they DO need help
          console.log('✅ User wants to continue (no = they do need help) - showing main menu');
          addBotMessage(t('perfectHereToHelp'));
          addBotMessage("", 'mainMenu', 1200);
          transitionToState(ConversationStates.MAIN_MENU);
        } else {
          console.log('❓ No clear intent in end confirmation - trying AI classification');

          // First check for specific service requests
          const specificIntent = IntentDetector.detectIntent(userMessage);
          if (specificIntent && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service'].includes(specificIntent.intent)) {
            // User made a specific request - handle it
            console.log('✅ Found specific service intent, handling request');
            handleQuickAction(userMessage, specificIntent.intent, true);
            return;
          }

          // Try AI classification for yes/no response
          const confirmationQuestion = t('endConversationConfirm');
          const aiYesNoClassification = await classifyYesNoWithAI(userMessage, confirmationQuestion);

          if (aiYesNoClassification === 'yes') {
            console.log('✅ AI classified as YES - ending conversation');
            addBotMessage(t('pleasureToChat'));
            transitionToState(ConversationStates.CONVERSATION_ENDED);
            setIsConversationEnded(true);
          } else if (aiYesNoClassification === 'no') {
            console.log('✅ AI classified as NO - continuing conversation');
            addBotMessage(t('perfectHereToHelp'));
            addBotMessage("", 'mainMenu', 1200);
            transitionToState(ConversationStates.MAIN_MENU);
          } else {
            console.log('❓ AI could not classify yes/no - using contextual response');
            // Final fallback: Use contextual AI response with conversation history
            await handleContextualResponse(userMessage);
          }
        }
        break;

      default:
        // Fallback to original logic for other states
        const fallbackIntentMatch = IntentDetector.detectIntent(userMessage);

        if (fallbackIntentMatch) {
          const lastBotMessage = messages.length > 0 ?
            messages.filter(m => !m.isUser).slice(-1)[0]?.text || '' : '';

          const conversationContext = {
            lastBotMessage,
            messageCount: messages.length,
            hasShownCalendly: messages.some(m => m.type === 'calendly')
          };

          const intentResponse = IntentDetector.getIntentResponse(fallbackIntentMatch, conversationContext);

          if (intentResponse) {
            const { messages: responseMessages, endConversation } = intentResponse;
            addBotMessages(responseMessages, 800);
            if (endConversation) {
              setIsConversationEnded(true);
            }
            return;
          }
        }

        // For Lithuanian users, prioritize AI response over knowledge base to ensure proper language
        if (locale === 'lt') {
          const aiResponse = await getAIResponse(userMessage);
          addBotMessage(aiResponse);
        } else {
          // For English users, try knowledge base first (faster response)
          const localAnswer = findKnowledgeMatch(userMessage);
          if (localAnswer) {
            addBotMessages([
              localAnswer,
              t('wouldYouLikeToKnowMoreServices')
            ]);
          } else {
            const aiResponse = await getAIResponse(userMessage);
            addBotMessage(aiResponse);
          }
        }
        break;
    }
  };

  return (
    <div className="chatbot-container fixed bottom-6 right-6 z-[9999]">
      {/* Kraken-style widget container - expands from the button itself */}
      <div className={`relative overflow-hidden bg-gradient-to-r from-pink-500 to-purple-600 shadow-xl ${
        isOpen || isClosing
          ? isClosing
            ? screenSize.isSmall
              ? 'chat-widget-collapsing-mobile'
              : 'chat-widget-collapsing'
            : screenSize.isSmall
              ? 'chat-widget-expanding-mobile'
              : 'chat-widget-expanding'
          : screenSize.isSmall
            ? 'w-12 h-12 rounded-full'
            : 'w-16 h-16 rounded-full'
      }`}>

        {/* Widget Button Icon Layer (always visible, changes based on state) */}
        <div className={`absolute inset-0 flex items-center justify-center transition-all duration-300 ${
          isOpen || isClosing ? 'opacity-0 pointer-events-none' : 'opacity-100'
        }`}>
          <button
            onClick={handleOpen}
            className="w-full h-full flex items-center justify-center focus:outline-none hover:scale-105 transition-transform"
            aria-label="Open chat"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`${screenSize.isSmall ? 'w-6 h-6' : 'w-8 h-8'} text-white`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
              />
            </svg>
          </button>
        </div>



        {/* Chat Content (visible when expanded) */}
        {(isOpen || isClosing) && (
          <div className={`absolute inset-0 bg-white rounded-2xl flex flex-col ${
            showChatContent && !isClosing ? 'chat-content-entering' : 'chat-content-leaving'
          }`}>
            <ChatHeader
              screenSize={screenSize}
              showCloseButton={true}
              onClose={handleClose}
              isOnline={!isConversationEnded}
            />

            <MessageList
              messages={messages}
              isBotTyping={isBotTyping}
              ref={chatContainerRef}
              screenSize={screenSize}
              onQuickAction={handleQuickAction}
            />

            {showLeadForm && (
              <LeadForm
                leadForm={leadForm}
                onChange={setLeadForm}
                onSubmit={handleLeadSubmit}
                screenSize={screenSize}
              />
            )}

            {isConversationEnded ? (
              <ChatEndedIndicator
                onRestart={() => {
                  setIsConversationEnded(false);
                  setMessages([]);
                  setConversationState(ConversationStates.WELCOME);
                  setUserContext({
                    name: '',
                    email: '',
                    lastIntent: null,
                    selectedService: null,
                    awaitingResponse: false
                  });
                  setInputValue('');
                  setIsBotTyping(false);
                  setShowLeadForm(false);
                  setShowCalendly(false);
                  setLastActivityTime(Date.now());
                  setTimeoutWarningShown(false);
                }}
                screenSize={screenSize}
              />
            ) : (
              <ChatInput
                inputValue={inputValue}
                onChange={setInputValue}
                onSend={handleSendMessage}
                disabled={isConversationEnded}
                screenSize={screenSize}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
}

// Chat Ended Indicator Component
interface ChatEndedIndicatorProps {
  onRestart: () => void;
  screenSize: ScreenSize;
}

function ChatEndedIndicator({ onRestart, screenSize }: ChatEndedIndicatorProps) {
  const t = useTranslations('Chatbot');
  const isSmall = screenSize?.isSmall || false;

  // Responsive sizing
  const containerPadding = isSmall
    ? screenSize.width <= 320 ? 'px-4 py-6' : 'px-5 py-8'
    : 'px-6 py-8';

  const textSize = isSmall
    ? screenSize.width <= 320 ? 'text-sm' : 'text-base'
    : 'text-base';

  const buttonPadding = isSmall
    ? screenSize.width <= 320 ? 'py-3 px-4' : 'py-3 px-6'
    : 'py-4 px-6';

  return (
    <div className={`border-t border-gray-100 bg-white ${containerPadding} flex flex-col items-center space-y-6`}>
      {/* Chat has ended text */}
      <div className="text-center">
        <span className={`text-gray-500 font-normal ${textSize}`}>
          {t('chatHasEnded')}
        </span>
      </div>

      {/* Start new chat button */}
      <button
        onClick={onRestart}
        className={`w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-xl font-medium hover:from-pink-600 hover:to-purple-700 transition-all duration-200 shadow-sm hover:shadow-md ${buttonPadding} ${isSmall ? 'text-sm' : 'text-base'}`}
      >
        {t('startNewChat')}
      </button>
    </div>
  );
}
