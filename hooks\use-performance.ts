"use client";

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { debounce, throttle } from '@/lib/performance-utils';

/**
 * Hook for stable references that don't change unless dependencies change
 */
export function useStableCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T {
  const callbackRef = useRef<T>(callback);
  
  useEffect(() => {
    callbackRef.current = callback;
  }, deps);
  
  return useCallback((...args: Parameters<T>) => {
    return callbackRef.current(...args);
  }, []) as T;
}

/**
 * Hook for memoized values with deep comparison
 */
export function useDeepMemo<T>(
  factory: () => T,
  deps: React.DependencyList
): T {
  const ref = useRef<{ deps: React.DependencyList; value: T }>();
  
  if (!ref.current || !areEqual(ref.current.deps, deps)) {
    ref.current = { deps, value: factory() };
  }
  
  return ref.current.value;
}

/**
 * Deep equality check for dependency arrays
 */
function areEqual(a: React.DependencyList, b: React.DependencyList): boolean {
  if (a.length !== b.length) return false;
  
  for (let i = 0; i < a.length; i++) {
    if (a[i] !== b[i]) {
      // For objects, do a shallow comparison
      if (typeof a[i] === 'object' && typeof b[i] === 'object' && a[i] !== null && b[i] !== null) {
        const objA = a[i] as Record<string, any>;
        const objB = b[i] as Record<string, any>;
        const keysA = Object.keys(objA);
        const keysB = Object.keys(objB);
        
        if (keysA.length !== keysB.length) return false;
        
        for (const key of keysA) {
          if (objA[key] !== objB[key]) return false;
        }
      } else {
        return false;
      }
    }
  }
  
  return true;
}

/**
 * Hook for preventing unnecessary re-renders with complex state
 */
export function useStableState<T>(
  initialState: T | (() => T),
  compareFn?: (prev: T, next: T) => boolean
): [T, (newState: T | ((prev: T) => T)) => void] {
  const [state, setState] = useState(initialState);
  const stateRef = useRef(state);
  
  const setStableState = useCallback((newState: T | ((prev: T) => T)) => {
    setState(prevState => {
      const nextState = typeof newState === 'function' 
        ? (newState as (prev: T) => T)(prevState)
        : newState;
      
      // Use custom compare function or default shallow comparison
      const areEqual = compareFn 
        ? compareFn(prevState, nextState)
        : prevState === nextState;
      
      if (areEqual) {
        return prevState; // Return same reference to prevent re-render
      }
      
      stateRef.current = nextState;
      return nextState;
    });
  }, [compareFn]);
  
  return [state, setStableState];
}

/**
 * Hook for intersection observer with performance optimizations
 */
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {},
  callback?: (entry: IntersectionObserverEntry) => void
) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null);
  
  const stableCallback = useStableCallback(callback || (() => {}), [callback]);
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        setIsIntersecting(entry.isIntersecting);
        setEntry(entry);
        stableCallback(entry);
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    );
    
    observer.observe(element);
    
    return () => {
      observer.unobserve(element);
      observer.disconnect();
    };
  }, [elementRef, stableCallback, options.threshold, options.rootMargin, options.root]);
  
  return { isIntersecting, entry };
}

/**
 * Hook for lazy loading components with intersection observer
 */
export function useLazyComponent<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [Component, setComponent] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const { isIntersecting } = useIntersectionObserver(elementRef, options);
  
  useEffect(() => {
    if (isIntersecting && !Component && !loading) {
      setLoading(true);
      importFn()
        .then(module => {
          setComponent(() => module.default);
          setError(null);
        })
        .catch(err => {
          setError(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [isIntersecting, Component, loading, importFn]);
  
  return { Component, loading, error };
}

/**
 * Hook for optimized media queries
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);
  
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);
    
    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };
    
    // Use the newer addEventListener if available, fallback to addListener
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handler);
      return () => mediaQuery.removeEventListener('change', handler);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handler);
      return () => mediaQuery.removeListener(handler);
    }
  }, [query]);
  
  return matches;
}

/**
 * Hook for optimized window dimensions with debouncing
 */
export function useWindowDimensions(debounceMs: number = 150) {
  const [dimensions, setDimensions] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0
  });
  
  const debouncedSetDimensions = useMemo(
    () => debounce((width: number, height: number) => {
      setDimensions({ width, height });
    }, debounceMs),
    [debounceMs]
  );
  
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const handleResize = () => {
      debouncedSetDimensions(window.innerWidth, window.innerHeight);
    };
    
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      debouncedSetDimensions.cancel();
    };
  }, [debouncedSetDimensions]);
  
  return dimensions;
}

/**
 * Hook for preventing layout thrashing during animations
 */
export function useLayoutStable() {
  const elementRef = useRef<HTMLElement>(null);
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    // Add will-change for better performance
    element.style.willChange = 'transform, opacity';
    
    return () => {
      element.style.willChange = 'auto';
    };
  }, []);
  
  return elementRef;
}

/**
 * Hook for RAF-based animations
 */
export function useAnimationFrame(callback: (deltaTime: number) => void, running: boolean = true) {
  const requestRef = useRef<number>();
  const previousTimeRef = useRef<number>();
  const callbackRef = useRef(callback);
  
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  useEffect(() => {
    if (!running) return;
    
    const animate = (time: number) => {
      if (previousTimeRef.current !== undefined) {
        const deltaTime = time - previousTimeRef.current;
        callbackRef.current(deltaTime);
      }
      previousTimeRef.current = time;
      requestRef.current = requestAnimationFrame(animate);
    };
    
    requestRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [running]);
  
  const stop = useCallback(() => {
    if (requestRef.current) {
      cancelAnimationFrame(requestRef.current);
    }
  }, []);
  
  return { stop };
}
