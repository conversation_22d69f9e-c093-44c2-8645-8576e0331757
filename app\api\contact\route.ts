import { NextResponse } from 'next/server';
import { getEmailService, createContactFormNotificationEmail, createContactFormConfirmationEmail } from '@/lib/email-service';
import { getAdminEmails } from '@/lib/email-config';

export async function GET() {
  return NextResponse.json({ message: 'Contact API endpoint. Use POST to submit contact form.' });
}

export async function POST(request: Request) {
  const { name, email, service, message, locale } = await request.json();

  // Basic validation
  if (!name || !email || !service || !message) {
    return NextResponse.json(
      { error: 'All fields are required' },
      { status: 400 }
    );
  }

  if (!/\S+@\S+\.\S+/.test(email)) {
    return NextResponse.json(
      { error: 'Valid email is required' },
      { status: 400 }
    );
  }

  try {
    const emailService = getEmailService();
    const userLocale = locale || 'en';

    // Load translations for email subjects
    const messages = await import(`../../../messages/${userLocale}.json`);
    const t = messages.default.EmailTemplates;

    // Create notification email for admin team
    const notificationHtml = await createContactFormNotificationEmail({
      name,
      email,
      service,
      message,
      locale: userLocale
    });

    // Create confirmation email for client
    const confirmationHtml = await createContactFormConfirmationEmail(name, userLocale);

    // Send both emails
    await emailService.sendMultipleEmails([
      {
        to: getAdminEmails(),
        subject: t.contactForm.notificationSubject.replace('{service}', service),
        html: notificationHtml
      },
      {
        to: email,
        subject: t.contactForm.confirmationSubject,
        html: confirmationHtml
      }
    ]);

    return NextResponse.json({
      message: 'Email sent successfully',
      success: true
    });
  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      {
        error: 'Failed to send email',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}