"use client"

import React, { useId } from "react"
import { motion } from "framer-motion"

interface GradientTracingProps {
  width: number
  height: number
  baseColor?: string
  gradientColors?: [string, string, string]
  animationDuration?: number
  strokeWidth?: number
  path?: string
}

export const GradientTracing: React.FC<GradientTracingProps> = ({
  width,
  height,
  baseColor = "black",
  gradientColors = ["#2EB9DF", "#2EB9DF", "#9E00FF"],
  animationDuration = 2,
  strokeWidth = 2,
  path = `M0,${height / 2} L${width},${height / 2}`,
}) => {
  const gradientId = `pulse-${useId()}`

  return (
    <div className="relative" style={{ width, height }}>
      <svg
        width={width}
        height={height}
        viewBox={`0 0 ${width} ${height}`}
        fill="none"
      >
        <path
          d={path}
          stroke={baseColor}
          strokeOpacity="0.2"
          strokeWidth={strokeWidth}
        />
        <path
          d={path}
          stroke={gradientColors[1]}
          strokeLinecap="round"
          strokeWidth={strokeWidth}
          strokeDasharray={`${width * 0.3} ${width * 0.7}`}
          style={{
            '--animation-duration': `${animationDuration}s`,
            '--dash-offset-start': `${width * 2}px`,
            animation: `dash-flow-${gradientId} var(--animation-duration) linear infinite`
          } as React.CSSProperties}
        />
      </svg>
      <style jsx>{`
        @keyframes dash-flow-${gradientId} {
          0% {
            stroke-dashoffset: var(--dash-offset-start);
          }
          100% {
            stroke-dashoffset: 0px;
          }
        }
      `}</style>
    </div>
  )
}
