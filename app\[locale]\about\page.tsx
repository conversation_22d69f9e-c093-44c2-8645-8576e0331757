"use client";

import React from 'react';
import { Link } from '@/i18n/navigation'; // Added import
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { Preview } from '@/components/about/Preview';
import { Handshake, ShieldCheck, Heart, Lock, ChevronRight } from 'lucide-react';
import { useTranslations } from 'next-intl';

// Using Lucide icons for consistency
const HonestyIcon = () => <Handshake className="w-6 h-6 text-white" />;
const ResponsibilityIcon = () => <ShieldCheck className="w-6 h-6 text-white" />;
const ClientCareIcon = () => <Heart className="w-6 h-6 text-white" />;
const TrustworthinessIcon = () => <Lock className="w-6 h-6 text-white" />;

const AboutPage = () => {
  const t = useTranslations('AboutPage');

  // Define text colors for dark background
  const headingColor = "text-white";
  const textColor = "text-purple-100/80"; // Light purple for paragraphs
  const cardTextColor = "text-purple-100/70"; // Slightly dimmer for card content

  return (
    // Apply dark gradient background to the main div
    <div className="min-h-screen overflow-x-hidden bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950">
      {/* Enhanced Hero Section with animation */}
      <div className="relative overflow-hidden h-[70vh] min-h-[500px] flex items-center justify-center">
        <div className="absolute inset-0 bg-gradient-to-b from-purple-950/90 to-purple-900/90 z-10"></div>
        
        {/* Animated background circles */}
        <div className="absolute w-96 h-96 bg-purple-600/20 rounded-full -top-20 -left-20 blur-3xl animate-pulse"></div>
        <div className="absolute w-96 h-96 bg-pink-600/20 rounded-full -bottom-20 -right-20 blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        
        <div className="container mx-auto px-4 relative z-20 text-center">
          <h1 className={`text-4xl md:text-6xl font-bold mb-6 ${headingColor} animate-fade-in`}>
            {t('heroTitle')}
          </h1>
          <p className={`text-lg md:text-xl ${textColor} max-w-3xl mx-auto mb-8 opacity-0 animate-fade-in-subtitle`} style={{ animationDelay: '0.8s' }}>
            {t('heroSubtitle')}
          </p>
          <div className="w-16 h-16 mx-auto mt-12 animate-bounce">
            <ChevronRight className="w-10 h-10 text-purple-300 rotate-90" />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12 md:py-20">
        {/* Story Timeline Section */}
        <section className="mb-20 md:mb-32 max-w-4xl mx-auto relative opacity-0 animate-fade-in-delayed">
          <h2 className={`text-3xl md:text-4xl font-semibold mb-10 text-center ${headingColor}`}>
            {t('ourStoryTitle')}
          </h2>
          
          {/* Timeline vertical line */}
          <div className="absolute left-0 md:left-1/2 top-24 bottom-0 w-1 bg-gradient-to-b from-pink-500 to-purple-500 transform -translate-x-1/2 hidden md:block"></div>
          
          {/* Timeline content */}
          <div className="space-y-12 relative">
            <div className="flex flex-col md:flex-row items-start">
              <div className="md:w-1/2 md:pr-12 md:text-right order-2 md:order-1">
                <div className={`text-base md:text-lg ${textColor} leading-relaxed`}>
                  <p className="mb-4">
                    {t('story1')}
                  </p>
                </div>
              </div>
              <div className="md:w-1/2 flex justify-start md:justify-center items-center order-1 md:order-2 mb-4 md:mb-0">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 flex items-center justify-center z-10">
                  <span className="text-white font-bold">1</span>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row items-start">
              <div className="md:w-1/2 flex justify-end md:justify-center items-center order-1 mb-4 md:mb-0">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 flex items-center justify-center z-10">
                  <span className="text-white font-bold">2</span>
                </div>
              </div>
              <div className="md:w-1/2 md:pl-12 order-2">
                <div className={`text-base md:text-lg ${textColor} leading-relaxed`}>
                  <p className="mb-4">
                    {t('story2')}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row items-start">
              <div className="md:w-1/2 md:pr-12 md:text-right order-2 md:order-1">
                <div className={`text-base md:text-lg ${textColor} leading-relaxed`}>
                  <p className="mb-4">
                    {t('story3')}
                  </p>
                </div>
              </div>
              <div className="md:w-1/2 flex justify-start md:justify-center items-center order-1 md:order-2 mb-4 md:mb-0">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 flex items-center justify-center z-10">
                  <span className="text-white font-bold">3</span>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row items-start">
              <div className="md:w-1/2 flex justify-end md:justify-center items-center order-1 mb-4 md:mb-0">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 flex items-center justify-center z-10">
                  <span className="text-white font-bold">4</span>
                </div>
              </div>
              <div className="md:w-1/2 md:pl-12 order-2">
                <div className={`text-base md:text-lg ${textColor} leading-relaxed`}>
                  <p className="mb-4">
                    {t('story4')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Added mt-16 for more space above & changed background */}
          {/* <div className="text-center mt-16 mb-16 inline-block bg-gradient-to-r from-pink-600 to-purple-600 px-4 py-2 rounded-lg border border-purple-500/30 backdrop-blur-sm">
            <p className={`text-lg md:text-xl text-white leading-relaxed font-semibold`}>
              {t('tagline')}
            </p>
          </div> */}
        </section>

        {/* Enhanced Team Section */}
        <section className="mb-20 md:mb-32">
          <h2 className={`text-3xl md:text-4xl font-semibold mb-6 text-center ${headingColor}`}>
            {t('teamTitle')}
          </h2>
          <p className={`text-lg ${textColor} text-center mb-12 max-w-3xl mx-auto`}>
            {t('teamSubtitle')}
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto">
            {/* Team Member 1 - Enhanced Card */}
            <div className="group relative h-full">
              <div className="absolute inset-0 bg-gradient-to-r from-pink-500/40 to-purple-500/40 rounded-xl blur-lg transform group-hover:scale-105 transition-all duration-300 opacity-70"></div>
              <div className="bg-purple-900/40 backdrop-blur-sm rounded-xl p-8 border border-purple-700/30 relative transform transition-all duration-300 group-hover:translate-y-[-10px] flex flex-col items-center h-full">
                {/* Profile Image with Border */}
                <div className="relative w-40 h-40 mb-6 rounded-full overflow-hidden border-4 border-purple-500/50 shadow-lg shadow-purple-500/30">
                  <Image
                    src="/profiles/Edgaras_photo2.jpg" // Path relative to /public folder
                    alt="Edgaras Pilybas profile picture"
                    fill
                    className="object-cover"
                  />
                </div>
                <h3 className={`text-2xl font-semibold ${headingColor} mb-2`}>Edgaras Pilybas</h3>
                <p className={`text-sm ${cardTextColor} opacity-80 mb-4`}>{t('edgarasRole')}</p>
                <p className={`${cardTextColor} text-center flex-grow flex items-center`}>
                  {t('edgarasDesc')}
                </p>
              </div>
            </div>
            
            {/* Team Member 2 - Enhanced Card */}
            <div className="group relative h-full">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/40 to-pink-500/40 rounded-xl blur-lg transform group-hover:scale-105 transition-all duration-300 opacity-70"></div>
              <div className="bg-purple-900/40 backdrop-blur-sm rounded-xl p-8 border border-purple-700/30 relative transform transition-all duration-300 group-hover:translate-y-[-10px] flex flex-col items-center h-full">
                {/* Profile Image with Border */}
                <div className="relative w-40 h-40 mb-6 rounded-full overflow-hidden border-4 border-purple-500/50 shadow-lg shadow-purple-500/30">
                  <Image
                    src="/profiles/Viliukas.jpg" // Path relative to /public folder
                    alt="Vilius Vaičius profile picture"
                    fill
                    className="object-cover object-[center_20%]" // object-center is default, you can change this
                    // Other common options:
                    // className="object-cover object-top" - shows top of image
                    // className="object-cover object-bottom" - shows bottom of image
                    // className="object-cover object-left" - shows left side of image
                    // className="object-cover object-right" - shows right side of image
                    // Or combine: object-top-left, object-top-right, etc.
                  />
                </div>
                <h3 className={`text-2xl font-semibold ${headingColor} mb-2`}>Vilius Vaičius</h3>
                <p className={`text-sm ${cardTextColor} opacity-80 mb-4`}>CTO</p>
                <p className={`${cardTextColor} text-center flex-grow flex items-center`}>
                {t('viliusDesc')}
                </p>
              </div>
            </div>
           
          </div>
        </section>

        {/* Enhanced Values Section */}
        <section className="mb-20 md:mb-32 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-800/10 to-pink-800/10 rounded-3xl -m-8"></div>
          <h2 className={`text-3xl md:text-4xl font-semibold mb-10 text-center ${headingColor} relative z-10`}>
            {t('valuesTitle')}
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 relative z-10">
            {/* Value Cards with Enhanced Hover Effects */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-pink-500/20 to-purple-500/20 rounded-xl blur-md transform group-hover:scale-110 transition-all duration-300 opacity-0 group-hover:opacity-100"></div>
              <div className="bg-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-purple-700/30 h-full flex flex-col items-center justify-center relative transform transition-all duration-300 group-hover:translate-y-[-5px] group-hover:bg-purple-900/40">
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-16 h-16 rounded-xl flex items-center justify-center mb-5 transform transition-all duration-300 group-hover:scale-110">
                  <HonestyIcon />
                </div>
                <h3 className={`text-xl font-semibold ${headingColor} mb-3`}>{t('honestyTitle')}</h3>
                <p className={`${cardTextColor} text-center`}>{t('honestyDesc')}</p>
              </div>
            </div>
            
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-pink-500/20 to-purple-500/20 rounded-xl blur-md transform group-hover:scale-110 transition-all duration-300 opacity-0 group-hover:opacity-100"></div>
              <div className="bg-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-purple-700/30 h-full flex flex-col items-center justify-center relative transform transition-all duration-300 group-hover:translate-y-[-5px] group-hover:bg-purple-900/40">
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-16 h-16 rounded-xl flex items-center justify-center mb-5 transform transition-all duration-300 group-hover:scale-110">
                  <ResponsibilityIcon />
                </div>
                <h3 className={`text-xl font-semibold ${headingColor} mb-3`}>{t('responsibilityTitle')}</h3>
                <p className={`${cardTextColor} text-center`}>{t('responsibilityDesc')}</p>
              </div>
            </div>
            
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-pink-500/20 to-purple-500/20 rounded-xl blur-md transform group-hover:scale-110 transition-all duration-300 opacity-0 group-hover:opacity-100"></div>
              <div className="bg-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-purple-700/30 h-full flex flex-col items-center justify-center relative transform transition-all duration-300 group-hover:translate-y-[-5px] group-hover:bg-purple-900/40">
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-16 h-16 rounded-xl flex items-center justify-center mb-5 transform transition-all duration-300 group-hover:scale-110">
                  <ClientCareIcon />
                </div>
                <h3 className={`text-xl font-semibold ${headingColor} mb-3`}>{t('clientCareTitle')}</h3>
                <p className={`${cardTextColor} text-center`}>{t('clientCareDesc')}</p>
              </div>
            </div>
            
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-pink-500/20 to-purple-500/20 rounded-xl blur-md transform group-hover:scale-110 transition-all duration-300 opacity-0 group-hover:opacity-100"></div>
              <div className="bg-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-purple-700/30 h-full flex flex-col items-center justify-center relative transform transition-all duration-300 group-hover:translate-y-[-5px] group-hover:bg-purple-900/40">
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-16 h-16 rounded-xl flex items-center justify-center mb-5 transform transition-all duration-300 group-hover:scale-110">
                  <TrustworthinessIcon />
                </div>
                <h3 className={`text-xl font-semibold ${headingColor} mb-3`}>{t('trustworthinessTitle')}</h3>
                <p className={`${cardTextColor} text-center`}>{t('trustworthinessDesc')}</p>
              </div>
            </div>
          </div>
        </section>

        {/* Interactive Preview Component */}
        <div className="my-20 md:my-32 relative">
          <div className="absolNute inset-0 bg-gradient-to-tr from-purple-800/20 to-pink-800/20 rounded-3xl -m-8"></div>
          <Preview />
        </div>

        {/* Enhanced Services Section */}
        <section className="mb-20 md:mb-32">
          <div className="relative overflow-hidden bg-purple-950/80 rounded-2xl border border-purple-800/30 py-16 px-6">
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
              <div className="absolute top-[-50%] left-[-50%] w-[200%] h-[200%] bg-[radial-gradient(circle,rgba(120,50,255,0.03)_0%,rgba(255,255,255,0)_60%)]"></div>
            </div>
            
            <div className="relative z-10">
              <h2 className={`text-3xl md:text-4xl font-semibold mb-10 text-center ${headingColor}`}>
                {t('servicesTitle')}
              </h2>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
                {/* Service Cards */}
                <div className="bg-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-purple-700/30 transform transition-all duration-300 hover:translate-y-[-5px] hover:bg-purple-900/40">
                  <h3 className={`text-xl font-semibold ${headingColor} mb-3`}>{t('webDevTitle')}</h3>
                  <ul className={`${textColor} list-disc list-inside marker:text-pink-400 space-y-2`}>
                    <li>{t('webDesign')}</li>
                    <li>{t('uiUxImplementation')}</li>
                  </ul>
                </div>
                
                <div className="bg-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-purple-700/30 transform transition-all duration-300 hover:translate-y-[-5px] hover:bg-purple-900/40">
                  <h3 className={`text-xl font-semibold ${headingColor} mb-3`}>{t('backendTitle')}</h3>
                  <ul className={`${textColor} list-disc list-inside marker:text-pink-400 space-y-2`}>
                    <li>{t('backendLogic')}</li>
                    <li>{t('processAutomation')}</li>
                  </ul>
                </div>

                <div className="bg-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-purple-700/30 transform transition-all duration-300 hover:translate-y-[-5px] hover:bg-purple-900/40">
                  <h3 className={`text-xl font-semibold ${headingColor} mb-3`}>{t('chatbotsTitle')}</h3>
                  <ul className={`${textColor} list-disc list-inside marker:text-pink-400 space-y-2`}>
                    <li>{t('aiIntegrations')}</li>
                    <li>{t('leadGeneration')}</li>
                  </ul>
                </div>
                
                <div className="bg-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-purple-700/30 transform transition-all duration-300 hover:translate-y-[-5px] hover:bg-purple-900/40">
                  <h3 className={`text-xl font-semibold ${headingColor} mb-3`}>{t('integrationsTitle')}</h3>
                  <ul className={`${textColor} list-disc list-inside marker:text-pink-400 space-y-2`}>
                    <li>{t('apis')}</li>
                    <li>{t('calendarBooking')}</li>
                  </ul>
                </div>

                <div className="bg-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-purple-700/30 transform transition-all duration-300 hover:translate-y-[-5px] hover:bg-purple-900/40">
                  <h3 className={`text-xl font-semibold ${headingColor} mb-3`}>{t('performanceTitle')}</h3>
                  <ul className={`${textColor} list-disc list-inside marker:text-pink-400 space-y-2`}>
                    <li>{t('optimization')}</li>
                    <li>{t('searchVisibility')}</li>
                  </ul>
                </div>

                <div className="bg-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-purple-700/30 transform transition-all duration-300 hover:translate-y-[-5px] hover:bg-purple-900/40">
                  <h3 className={`text-xl font-semibold ${headingColor} mb-3`}>{t('supportTitle')}</h3>
                  <ul className={`${textColor} list-disc list-inside marker:text-pink-400 space-y-2`}>
                    <li>{t('maintenance')}</li>
                    <li>{t('updatesImprovements')}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>
{/* 

        {/* Enhanced CTA */}
        <section className="mb-16 relative">
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-pink-600 to-purple-600 py-16 px-6 group">
            {/* Animated background elements */}
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(255,255,255,0.1),rgba(255,255,255,0))] opacity-70"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-pink-600/50 to-purple-600/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            
            {/* Animated glow effects */}
            <div className="absolute -top-32 -left-32 w-64 h-64 bg-pink-500/30 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-32 -right-32 w-64 h-64 bg-purple-500/30 rounded-full blur-3xl"></div>
            
            <div className="relative z-10 text-center">
              <h2 className={`text-3xl md:text-4xl font-semibold mb-8 ${headingColor}`}>
                {t('ctaFinalTitle')}
              </h2>
              <p className={`text-lg ${textColor} max-w-2xl mx-auto mb-10`}>
                {t('ctaFinalDesc')}
              </p>
              <Link href="/contact">
                <Button
                  size="lg"
                  className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white shadow-lg shadow-purple-950/30 transition-all duration-300 hover:scale-105 px-8 py-6 text-lg border border-white/20"
                >
                  {t('getFreeQuote')}
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default AboutPage;
