@tailwind base;
@tailwind components;
@tailwind utilities;

/* Chatbot CSS Variables */
:root {
  --chat-font-size: 16px;
  --chat-padding: 16px;
}

/* Custom animations for chatbot */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Kraken-style widget transition animations */
@keyframes chatWidgetExpand {
  0% {
    width: 60px;
    height: 60px;
    border-radius: 50%;
  }
  50% {
    width: 60px;
    height: 200px;
    border-radius: 30px;
  }
  100% {
    width: 380px;
    height: 600px;
    border-radius: 16px;
  }
}

@keyframes chatWidgetExpandMobile {
  0% {
    width: 50px;
    height: 50px;
    border-radius: 50%;
  }
  50% {
    width: 50px;
    height: 150px;
    border-radius: 25px;
  }
  100% {
    width: calc(100vw - 2rem);
    height: calc(100vh - 8rem);
    border-radius: 16px;
  }
}

@keyframes chatWidgetCollapse {
  0% {
    width: 380px;
    height: 600px;
    border-radius: 16px;
  }
  50% {
    width: 60px;
    height: 200px;
    border-radius: 30px;
  }
  100% {
    width: 60px;
    height: 60px;
    border-radius: 50%;
  }
}

@keyframes chatWidgetCollapseMobile {
  0% {
    width: calc(100vw - 2rem);
    height: calc(100vh - 8rem);
    border-radius: 16px;
  }
  50% {
    width: 50px;
    height: 150px;
    border-radius: 25px;
  }
  100% {
    width: 50px;
    height: 50px;
    border-radius: 50%;
  }
}

@keyframes iconToChat {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-100px) scale(0.8);
    opacity: 0;
  }
  100% {
    transform: translateY(-200px) scale(0);
    opacity: 0;
  }
}

@keyframes chatToIcon {
  0% {
    transform: translateY(-200px) scale(0);
    opacity: 0;
  }
  50% {
    transform: translateY(-100px) scale(0.8);
    opacity: 0;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes chatContentSlideIn {
  0% {
    transform: translateY(200px);
    opacity: 0;
  }
  50% {
    transform: translateY(100px);
    opacity: 0.3;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes chatContentSlideOut {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  50% {
    transform: translateY(100px);
    opacity: 0.3;
  }
  100% {
    transform: translateY(200px);
    opacity: 0;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.chat-widget-expanding {
  animation: chatWidgetExpand 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-widget-expanding-mobile {
  animation: chatWidgetExpandMobile 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-widget-collapsing {
  animation: chatWidgetCollapse 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-widget-collapsing-mobile {
  animation: chatWidgetCollapseMobile 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-icon-hiding {
  animation: iconToChat 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-icon-showing {
  animation: chatToIcon 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-content-entering {
  animation: chatContentSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-content-leaving {
  animation: chatContentSlideOut 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Smooth scrolling for chat container */
.overflow-y-auto {
  scroll-behavior: smooth;
}

/* Responsive classes for chatbot */
.responsive-text {
  font-size: var(--chat-font-size);
}

.responsive-padding {
  padding: var(--chat-padding);
}

/* Hide non-essential elements on small screens */
@media (max-width: 375px) {
  .non-essential {
    display: none;
  }
}

/* Ensure chatbot styles are properly applied */
.chatbot-container {
  position: fixed !important;
  bottom: 1.5rem !important;
  right: 1.5rem !important;
  z-index: 9999 !important;
}

/* Mobile-specific positioning for chatbot */
@media (max-width: 768px) {
  .chatbot-container {
    bottom: 1rem !important;
    right: 1rem !important;
  }
}

/* Add this at the top level of your CSS */
html {
  background: linear-gradient(to bottom right, rgb(88, 28, 135), rgb(219, 39, 119)); /* purple-950 to pink-600 */
  /* Ensure the background stays fixed while scrolling */
  background-attachment: fixed;
  /* Prevent horizontal scrolling and layout shifts */
  overflow-x: hidden;
  width: 100%;
  height: 100%;
  /* Control overscroll behavior on mobile */
  overscroll-behavior: auto;
  /* Ensure the background color matches theme-color for mobile browsers */
  background-color: linear-gradient(to bottom right, rgb(88, 28, 135), rgb(219, 39, 119)); /* Fallback color matching theme-color */
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Ensure body covers full viewport and prevents layout shifts */
    min-height: 100vh;
    width: 100%;
    overflow-x: hidden;
    position: relative;
    /* Match the html background for consistent overscroll */
    background: linear-gradient(to bottom right, rgb(88, 28, 135), rgb(219, 39, 119));
    background-attachment: fixed;
    /* Control overscroll behavior */
    overscroll-behavior: none;
  }
}

/* Prevent layout shifts from dropdown menus */
[data-radix-popper-content-wrapper] {
  /* Ensure dropdowns don't cause viewport extension */
  max-width: 100vw !important;
}

/* Prevent horizontal scrolling from specific elements that might cause issues */
body, html {
  box-sizing: border-box;
}

/* Prevent language switcher dropdown from extending viewport */
[data-radix-dropdown-menu-content].bg-purple-900 {
  max-width: calc(100vw - 32px) !important;
  box-sizing: border-box !important;
}

/* Add animations for packages section */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation utility classes */
.animate-slideInFromLeft {
  animation: slideInFromLeft 0.8s ease-out forwards;
}

.animate-slideInFromRight {
  animation: slideInFromRight 0.8s ease-out forwards;
}

.animate-slideInFromBottom {
  animation: slideInFromBottom 0.8s ease-out forwards;
}

/* Add animations for hero section */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Animation utility classes */
.animate-fadeIn {
  animation: fadeIn 1s ease-out forwards;
}

/* StarBorder Animations - Improved for better corner stability */
@keyframes star-movement-bottom {
  0% {
    transform: translateX(0) rotate(0deg);
  }
  50% {
    transform: translateX(-25%) rotate(90deg);
  }
  100% {
    transform: translateX(-45%) rotate(175deg); /* Reduced rotation to prevent corner issues */
  }
}

@keyframes star-movement-top {
  0% {
    transform: translateX(0) rotate(0deg);
  }
  50% {
    transform: translateX(25%) rotate(-90deg);
  }
  100% {
    transform: translateX(45%) rotate(-175deg); /* Reduced rotation to prevent corner issues */
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Add fade-in animation for About page */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
  /* Removed opacity: 0 to prevent white screen flashes */
}

/* Delayed fade-in animation for story section */
.animate-fade-in-delayed {
  animation: fadeIn 1.2s ease-out forwards;
  animation-delay: 1.5s;
}

/* Subtitle animation with shorter delay */
.animate-fade-in-subtitle {
  animation: fadeIn 1s ease-out forwards;
  animation-delay: 0.8s;
}

/* FAQ Content Styling */
.faq-answer-content {
  max-width: 100%;
  overflow-x: hidden;
}

/* FAQ Question Styling */
.faq-question-container {
  min-height: 80px;
  width: 100%;
  display: block;
}

.faq-question-container h3 {
  width: 100%;
  display: block;
  white-space: normal !important;
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  hyphens: auto !important;
  text-overflow: initial !important;
}

/* Mobile-specific styles */
@media (max-width: 640px) {
  .faq-question-container {
    padding-right: 40px;
  }

  /* Mobile footer optimizations */
  footer {
    padding-bottom: calc(env(safe-area-inset-bottom) + 1rem) !important;
  }

  /* Ensure footer links have adequate touch targets on mobile */
  footer a {
    min-height: 44px;
    display: flex;
    align-items: center;
    padding: 8px 0;
  }

  /* Mobile language switcher improvements */
  [data-radix-dropdown-menu-trigger] {
    min-height: 44px !important;
    min-width: 44px !important;
  }

  [data-radix-dropdown-menu-content] {
    margin-right: 16px !important;
    max-width: calc(100vw - 32px) !important;
  }
}

.faq-answer-content ul,
.faq-answer-content ol {
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.faq-answer-content li {
  margin-left: 1.5rem;
  position: relative;
  max-width: calc(100% - 3rem);
  overflow-wrap: break-word;
  word-break: break-word;
  margin-bottom: 0.75rem;
}

/* No mobile connections for approach page */

/* CPU Architecture Component Animations */
@keyframes cpu-move-1 {
  0% { transform: translate(10px, 20px); }
  30% { transform: translate(45px, 35px); }
  100% { transform: translate(100px, 50px); }
}

@keyframes cpu-move-2 {
  0% { transform: translate(180px, 10px); }
  35% { transform: translate(140px, 30px); }
  100% { transform: translate(100px, 50px); }
}

@keyframes cpu-move-3 {
  0% { transform: translate(130px, 20px); }
  40% { transform: translate(120px, 35px); }
  100% { transform: translate(100px, 50px); }
}

@keyframes cpu-move-4 {
  0% { transform: translate(170px, 80px); }
  50% { transform: translate(140px, 65px); }
  100% { transform: translate(100px, 50px); }
}

@keyframes cpu-move-5 {
  0% { transform: translate(135px, 65px); }
  45% { transform: translate(120px, 55px); }
  100% { transform: translate(100px, 50px); }
}

@keyframes cpu-move-6 {
  0% { transform: translate(94.8px, 95px); }
  40% { transform: translate(95px, 70px); }
  100% { transform: translate(100px, 50px); }
}

@keyframes cpu-move-7 {
  0% { transform: translate(88px, 88px); }
  50% { transform: translate(90px, 65px); }
  100% { transform: translate(100px, 50px); }
}

@keyframes cpu-move-8 {
  0% { transform: translate(30px, 30px); }
  40% { transform: translate(60px, 40px); }
  100% { transform: translate(100px, 50px); }
}

.cpu-architecture.cpu-line-1 {
  animation: cpu-move-1 4s infinite ease-in-out;
}

.cpu-architecture.cpu-line-2 {
  animation: cpu-move-2 4.5s infinite ease-in-out;
}

.cpu-architecture.cpu-line-3 {
  animation: cpu-move-3 3.8s infinite ease-in-out;
}

.cpu-architecture.cpu-line-4 {
  animation: cpu-move-4 4.2s infinite ease-in-out;
}

.cpu-architecture.cpu-line-5 {
  animation: cpu-move-5 5s infinite ease-in-out;
}

.cpu-architecture.cpu-line-6 {
  animation: cpu-move-6 3.5s infinite ease-in-out;
}

.cpu-architecture.cpu-line-7 {
  animation: cpu-move-7 4.7s infinite ease-in-out;
}

.cpu-architecture.cpu-line-8 {
  animation: cpu-move-8 4.3s infinite ease-in-out;
}

/* Floating label animations for CPU block */
@keyframes float-label {
  0%, 100% {
    opacity: 0.7;
    transform: translateY(0px);
  }
  50% {
    opacity: 1;
    transform: translateY(-5px);
  }
}

@keyframes float-label-alt {
  0%, 100% {
    opacity: 0.7;
    transform: translateY(0px);
  }
  50% {
    opacity: 1;
    transform: translateY(5px);
  }
}

.cpu-float-label {
  animation: float-label 6s infinite ease-in-out;
}

.cpu-float-label-alt {
  animation: float-label-alt 5s infinite ease-in-out;
}

/* Gentle pulse animation for chatbot status indicator */
@keyframes gentlePulse {
  0%, 90%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  95% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* Floating avatar animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes shadow-float {
  0%, 100% {
    transform: translateX(-50%) translateY(4px) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateX(-50%) translateY(12px) scale(1.15);
    opacity: 0.4;
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-shadow-float {
  animation: shadow-float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-shadow-float-slow {
  animation: shadow-float 4s ease-in-out infinite;
}

.animate-gentle-pulse {
  animation: gentlePulse 5s infinite ease-in-out;
}


